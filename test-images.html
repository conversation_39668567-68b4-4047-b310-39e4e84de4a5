<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .test-item {
            background: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-image {
            width: 100%;
            height: 150px;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            border-radius: 4px;
            border: 1px solid #ddd;
            margin-bottom: 10px;
        }
        .test-info {
            font-size: 14px;
            color: #666;
        }
        .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .status.loading {
            background-color: #fff3cd;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Panama Hat List2 图片测试</h1>
        <p>测试 assets/img/cap/panama/list2/ 目录中的图片是否能正确加载</p>
        
        <div class="test-grid" id="testGrid">
            <!-- 测试项目将通过JavaScript动态生成 -->
        </div>
    </div>

    <script>
        // 测试图片加载的函数
        function testImageLoad(imagePath) {
            return new Promise((resolve) => {
                const img = new Image();
                img.onload = () => resolve({ success: true, path: imagePath });
                img.onerror = () => resolve({ success: false, path: imagePath });
                img.src = imagePath;
            });
        }

        // 创建测试项目
        function createTestItem(imageNumber, result) {
            const testItem = document.createElement('div');
            testItem.className = 'test-item';
            
            const imagePath = `assets/img/cap/panama/list2/${imageNumber}.jpg`;
            const statusClass = result.success ? 'success' : 'error';
            const statusText = result.success ? '加载成功' : '加载失败';
            
            testItem.innerHTML = `
                <div class="test-image" style="background-image: url('${imagePath}')"></div>
                <div class="test-info">
                    <div>图片 ${imageNumber}</div>
                    <div>路径: ${imagePath}</div>
                    <div class="status ${statusClass}">${statusText}</div>
                </div>
            `;
            
            return testItem;
        }

        // 执行测试
        async function runImageTests() {
            const testGrid = document.getElementById('testGrid');
            const imageNumbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];
            
            console.log('开始测试图片加载...');
            
            for (const num of imageNumbers) {
                const imagePath = `assets/img/cap/panama/list2/${num}.jpg`;
                
                // 先创建加载中的状态
                const loadingItem = document.createElement('div');
                loadingItem.className = 'test-item';
                loadingItem.innerHTML = `
                    <div class="test-image" style="background-color: #f0f0f0;"></div>
                    <div class="test-info">
                        <div>图片 ${num}</div>
                        <div>路径: ${imagePath}</div>
                        <div class="status loading">测试中...</div>
                    </div>
                `;
                testGrid.appendChild(loadingItem);
                
                // 测试图片加载
                const result = await testImageLoad(imagePath);
                console.log(`图片 ${num} 测试结果:`, result);
                
                // 替换为最终结果
                const finalItem = createTestItem(num, result);
                testGrid.replaceChild(finalItem, loadingItem);
                
                // 添加小延迟，让用户看到加载过程
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            console.log('图片测试完成');
        }

        // 页面加载完成后开始测试
        document.addEventListener('DOMContentLoaded', runImageTests);
    </script>
</body>
</html>
