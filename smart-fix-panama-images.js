// 智能修复 PanamaCap-list1.html 图片显示问题
// 这个脚本会检测实际存在的图片文件并更新HTML

const fs = require('fs');
const path = require('path');

// 读取HTML文件
let content = fs.readFileSync('PanamaCap-list1.html', 'utf8');

// 检查图片文件是否存在的函数
function checkImageExists(imagePath) {
    try {
        return fs.existsSync(imagePath);
    } catch (error) {
        return false;
    }
}

// 为每个产品ID查找实际存在的图片格式
function findExistingImageFormat(productId) {
    const basePath = `assets/img/cap/panama/list1/${productId}`;
    const formats = ['jpg', 'jpeg', 'png', 'webp', 'avif'];
    
    for (const format of formats) {
        const fullPath = `${basePath}.${format}`;
        if (checkImageExists(fullPath)) {
            console.log(`✅ 找到图片: ${fullPath}`);
            return format;
        }
    }
    
    console.log(`❌ 未找到图片: ${basePath}.*`);
    return null;
}

// 产品ID列表
const productIds = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];

console.log('🔍 开始检测图片文件...\n');

// 存储修复信息
const fixes = [];

// 检测每个产品的图片
productIds.forEach(id => {
    const existingFormat = findExistingImageFormat(id);
    
    if (existingFormat) {
        // 创建所有可能的错误格式替换
        const possibleWrongFormats = ['jpg', 'jpeg', 'png', 'webp'];
        
        possibleWrongFormats.forEach(wrongFormat => {
            if (wrongFormat !== existingFormat) {
                const wrongPattern = `data-image-base="assets/img/cap/panama/list1/${id}" style="background-image: url('assets/img/cap/panama/list1/${id}.${wrongFormat}');"`;
                const correctPattern = `data-image-base="assets/img/cap/panama/list1/${id}" style="background-image: url('assets/img/cap/panama/list1/${id}.${existingFormat}');"`;
                
                if (content.includes(wrongPattern)) {
                    content = content.replace(wrongPattern, correctPattern);
                    fixes.push(`Product ${id}: ${wrongFormat} → ${existingFormat}`);
                    console.log(`🔧 修复 Product ${id}: ${wrongFormat} → ${existingFormat}`);
                }
            }
        });
        
        // 也检查没有style属性的情况
        const noStylePattern = `<div class="product-image" data-image-base="assets/img/cap/panama/list1/${id}"></div>`;
        const withStylePattern = `<div class="product-image" data-image-base="assets/img/cap/panama/list1/${id}" style="background-image: url('assets/img/cap/panama/list1/${id}.${existingFormat}');"></div>`;
        
        if (content.includes(noStylePattern)) {
            content = content.replace(noStylePattern, withStylePattern);
            fixes.push(`Product ${id}: 添加了 ${existingFormat} 背景图片`);
            console.log(`🔧 为 Product ${id} 添加背景图片: ${existingFormat}`);
        }
    } else {
        console.log(`⚠️  Product ${id}: 图片文件不存在，跳过修复`);
    }
});

// 写回文件
if (fixes.length > 0) {
    fs.writeFileSync('PanamaCap-list1.html', content);
    console.log(`\n🎉 修复完成！共修复了 ${fixes.length} 个问题:`);
    fixes.forEach(fix => console.log(`   - ${fix}`));
} else {
    console.log('\n✅ 没有发现需要修复的问题');
}

console.log('\n📋 建议检查以下目录中的图片文件:');
console.log('   assets/img/cap/panama/list1/');
console.log('   确保文件名格式正确 (1.jpg, 2.png, 等等)');

// 生成图片文件检查报告
console.log('\n📊 图片文件检查报告:');
productIds.forEach(id => {
    const existingFormat = findExistingImageFormat(id);
    if (existingFormat) {
        console.log(`   Product ${id}: ✅ ${existingFormat}`);
    } else {
        console.log(`   Product ${id}: ❌ 缺失`);
    }
});
