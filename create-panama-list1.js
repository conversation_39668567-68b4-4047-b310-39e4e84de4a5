// 创建新的 PanamaCap-list1.html，基于 BaseballCap-list1.html
const fs = require('fs');

console.log('🔄 开始创建 PanamaCap-list1.html...');

// 读取 BaseballCap-list1.html
let content = fs.readFileSync('D:\\Users\\dp\\Desktop\\编写帽子网站\\帽子网站编写\\代码编写\\25-05-29\\BaseballCap-list1.html', 'utf8');

console.log('✅ 已读取 BaseballCap-list1.html');

// 进行必要的替换
const replacements = [
    // 标题替换
    {
        from: '<title data-i18n="our_collection_title">Our Collection - Baseball Caps</title>',
        to: '<title data-i18n="our_collection_title">Our Collection - Panama Hats</title>'
    },
    // 页面标题替换
    {
        from: '<h1 class="text-xl font-bold text-white" data-i18n="panama_hats_collection">Baseball Caps Collection</h1>',
        to: '<h1 class="text-xl font-bold text-white" data-i18n="panama_hats_collection">Panama Hats Collection</h1>'
    },
    // 返回按钮链接替换
    {
        from: '<a href="./BaseballCap.html" class="btn-back">',
        to: '<a href="./PanamaCap.html" class="btn-back">'
    },
    // 图片路径替换 - 将所有 baseball 路径替换为 panama
    {
        from: /assets\/img\/cap\/baseball\/list1/g,
        to: 'assets/img/cap/panama/list1'
    },
    // 产品数据属性中的图片路径替换
    {
        from: /data-image="assets\/img\/cap\/baseball\/list1/g,
        to: 'data-image="assets/img/cap/panama/list1'
    },
    // 产品基础图片路径替换
    {
        from: /data-image-base="assets\/img\/cap\/baseball\/list1/g,
        to: 'data-image-base="assets/img/cap/panama/list1'
    },
    // 背景图片路径替换
    {
        from: /background-image: url\('assets\/img\/cap\/baseball\/list1/g,
        to: "background-image: url('assets/img/cap/panama/list1"
    }
];

console.log('🔧 开始应用替换...');

let replacedCount = 0;
replacements.forEach((replacement, index) => {
    const beforeLength = content.length;
    if (replacement.from instanceof RegExp) {
        content = content.replace(replacement.from, replacement.to);
    } else {
        content = content.replace(new RegExp(replacement.from, 'g'), replacement.to);
    }
    const afterLength = content.length;
    
    if (beforeLength !== afterLength) {
        replacedCount++;
        console.log(`✅ 替换 ${index + 1}: 成功`);
    } else {
        console.log(`⚠️  替换 ${index + 1}: 未找到匹配项`);
    }
});

// 写入新文件
const outputPath = 'D:\\Users\\dp\\Desktop\\编写帽子网站\\帽子网站编写\\代码编写\\25-05-29\\PanamaCap-list1-corrected.html';
fs.writeFileSync(outputPath, content);

console.log(`\n🎉 创建完成！`);
console.log(`   - 输出文件: PanamaCap-list1-corrected.html`);
console.log(`   - 应用了 ${replacedCount} 个替换`);
console.log(`   - 文件大小: ${Math.round(content.length / 1024)} KB`);

console.log('\n📋 主要修改:');
console.log('   ✅ 标题改为 Panama Hats');
console.log('   ✅ 返回链接改为 PanamaCap.html');
console.log('   ✅ 所有图片路径改为 panama/list1');
console.log('   ✅ 保持所有原有功能');

console.log('\n🚀 下一步:');
console.log('   1. 检查新文件内容');
console.log('   2. 如果满意，可以替换原有的 PanamaCap-list1.html');
console.log('   3. 确保 assets/img/cap/panama/list1/ 目录存在');
console.log('   4. 添加相应的产品图片');
