// 购物车和收藏功能的JavaScript实现

document.addEventListener('DOMContentLoaded', function() {
    // 初始化购物车计数
    let cartCount = 0;
    const cartCountElement = document.querySelector('.cart-count');
    
    // 如果存在本地存储的购物车数据，则恢复它
    if (localStorage.getItem('cartCount')) {
        cartCount = parseInt(localStorage.getItem('cartCount'));
        updateCartCount();
    }
    
    // 为所有购物车按钮添加点击事件
    const cartButtons = document.querySelectorAll('.cart.button');
    cartButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            cartCount++;
            updateCartCount();
            // 保存到本地存储
            localStorage.setItem('cartCount', cartCount);
        });
    });
    
    // 为所有收藏按钮添加点击事件
    const favoriteButtons = document.querySelectorAll('.follow.button');
    favoriteButtons.forEach(button => {
        // 检查是否已经收藏（从本地存储中获取）
        const productId = button.closest('.grid--cell').getAttribute('data-product-id') || 
                         button.closest('.grid--item').getAttribute('data-product-id') || 
                         Math.random().toString(36).substring(2, 15);
        
        if (localStorage.getItem('favorite_' + productId) === 'true') {
            button.classList.add('active');
            button.querySelector('i').classList.remove('fa-plus');
            button.querySelector('i').classList.add('fa-heart');
            button.querySelector('i').style.color = '#ff0000';
        }
        
        button.addEventListener('click', function(e) {
            e.preventDefault();
            this.classList.toggle('active');
            
            const icon = this.querySelector('i');
            if (this.classList.contains('active')) {
                // 收藏状态
                icon.classList.remove('fa-plus');
                icon.classList.add('fa-heart');
                icon.style.color = '#ff0000'; // 红色心形
                localStorage.setItem('favorite_' + productId, 'true');
            } else {
                // 未收藏状态
                icon.classList.remove('fa-heart');
                icon.classList.add('fa-plus');
                icon.style.color = ''; // 恢复默认颜色
                localStorage.setItem('favorite_' + productId, 'false');
            }
        });
    });
    
    // 更新购物车计数显示
    function updateCartCount() {
        if (cartCountElement) {
            cartCountElement.textContent = cartCount;
            // 如果计数为0，可以隐藏计数元素
            if (cartCount === 0) {
                cartCountElement.style.display = 'none';
            } else {
                cartCountElement.style.display = 'flex';
            }
        }
    }
});