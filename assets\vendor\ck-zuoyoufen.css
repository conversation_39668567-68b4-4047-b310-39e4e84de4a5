@import url("https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap");

:root {
  --left-bg-color: rgba(154, 154, 154, 0.8);
  --right-bg-color: rgba(43, 43, 43, 0.8);
  --left-btn-hover-color: rgb(86, 85, 148);
  --right-btn-hover-color: rgb(48, 103, 48);
  --hover-width: 75%;
  --minimize-width: 25%;
  --transition-speed: 1s;
}

* {
  box-sizing: border-box;
}

.about-section {
  font-family: "Roboto", sans-serif;
  height: 100vh;
  overflow: hidden;
  margin: 0;
  margin-top: 0; /* 或者设置为您希望的间距 */
  background: #101522;
	background-blend-mode: hard-light;
	background-image: radial-gradient(circle at 20% 20%, #ffcc7066 10%, #ffcc7000 50%), radial-gradient(circle at 80% 80%, #0033ff66 10%, #0033ff00 50%),
		radial-gradient(ellipse at 35% 70%, #00ff4866 10%, #00ff4800 50%), radial-gradient(ellipse at 70% 35%, #ff005d66 10%, #ff005d00 60%);
	background-size: 250% 250%;
	animation: background-animation 30s infinite;
}


.about-section h1 {
  font-size: 4rem;
  color: #fff;
  position: absolute;
  left: 50%;
  top: 20%;
  transform: translateX(-50%);
  white-space: nowrap;
}

.btn {
  color: #fff;
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  left: 50%;
  top: 40%;
  transform: translateX(-50%);
  border: #fff solid 0.2rem;
  text-decoration: none;
  font-size: 1rem;
  font-weight: bold;
  text-transform: uppercase;
  width: 15rem;
  padding: 1.5rem;
  border-radius: 50rem; /* 圆形的边框半径 */
  animation: ripple 1.5s linear infinite; /* 应用涟漪动画 */
  transition: all 0.7s ease; /* 所有属性的过渡效果 */
  
}

.split.left .btn:hover {
  background-color: var(--left-btn-hover-color);
  border-color: var(--left-btn-hover-color);
}

.split.right .btn:hover {
  background-color: var(--right-btn-hover-color);
  border-color: var(--right-btn-hover-color);
}

.about-section.containersx {
  position: relative;
  width: 100%;
  height: 100%;
  background: #b9b4b4;
  padding: 0; /* 根据您的设计调整 */
}

.split {
  position: absolute;
  width: 50%;
  height: 100%;
  overflow: hidden;
}

.split.left {
  width: 100%; /* 确保宽度适应网页宽度 */
  height: 60%; /* 设置高度为100% */
  left: 0; /* 保持原有的left值 */
  background: url("../img/Panamahats2.jpg") no-repeat center / cover; /* 保持原有的背景属性 */
  box-shadow: 0px 35px 75px #5c551171; /* 添加阴影效果 */
}

.split.left::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: var(--left-bg-color);
}

.split.right {
  right: 0;
  height: 60%; /* 设置高度为100% */
  background: url("../img/baseballcaps2.jpg")
    no-repeat center / cover;
  box-shadow: 0px 35px 65px rgba(92, 86, 17, 0.493); /* 添加阴影效果 */
}

.split.right::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: var(--right-bg-color);
}

.split.left,
.split.right,
.split.left::before,
.split.right::before {
  transition: all var(--transition-speed) ease-in-out;
}


.hover-left .left {
  width: var(--hover-width);
}

.hover-left .right {
  width: var(--minimize-width);
}

.hover-right .right {
  width: var(--hover-width);
}

.hover-right .left {
  width: var(--minimize-width);
}

@media (max-width: 800px) {
  .about-section h1 {
    font-size: 2rem;
    top: 30%;
  }
  .about-section.btn {
    padding: 1.2rem;
    width: 12rem;
  }
}
/* 定义涟漪动画的关键帧 */
@keyframes ripple {
  0% {
      box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.3), /* 初始化涟漪效果的阴影 */
        0 0 0 1px rgba(255, 255, 255, 0.3),
        0 0 0 3px rgba(255, 255, 255, 0.3),
        0 0 0 5px rgba(255, 255, 255, 0.3);
  }
  100% {
      box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.3), /* 动画结束时的阴影效果 */
        0 0 0 4px rgba(255, 255, 255, 0.3),
        0 0 0 20px rgba(255, 255, 255, 0), /* 涟漪扩散效果 */
        0 0 0 30px rgba(255, 255, 255, 0); /* 涟漪扩散效果 */
  }
}

@keyframes background-animation {
	0% {
		background-position: 5% 0%;
	}
	25% {
		background-position: 20% 80%;
	}
	50% {
		background-position: 96% 100%;
	}
	75% {
		background-position: 80% 10%;
	}
	100% {
		background-position: 5% 0%;
	}