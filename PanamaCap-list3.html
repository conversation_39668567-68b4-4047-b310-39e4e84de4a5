<script type="text/javascript">
        var gk_isXlsx = false;
        var gk_xlsxFileLookup = {};
        var gk_fileData = {};
        function filledCell(cell) {
          return cell !== '' && cell != null;
        }
        function loadFileData(filename) {
        if (gk_isXlsx && gk_xlsxFileLookup[filename]) {
            try {
                var workbook = XLSX.read(gk_fileData[filename], { type: 'base64' });
                var firstSheetName = workbook.SheetNames[0];
                var worksheet = workbook.Sheets[firstSheetName];

                // Convert sheet to JSON to filter blank rows
                var jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, blankrows: false, defval: '' });
                // Filter out blank rows (rows where all cells are empty, null, or undefined)
                var filteredData = jsonData.filter(row => row.some(filledCell));

                // Heuristic to find the header row by ignoring rows with fewer filled cells than the next row
                var headerRowIndex = filteredData.findIndex((row, index) =>
                  row.filter(filledCell).length >= filteredData[index + 1]?.filter(filledCell).length
                );
                // Fallback
                if (headerRowIndex === -1 || headerRowIndex > 25) {
                  headerRowIndex = 0;
                }

                // Convert filtered JSON back to CSV
                var csv = XLSX.utils.aoa_to_sheet(filteredData.slice(headerRowIndex)); // Create a new sheet from filtered array of arrays
                csv = XLSX.utils.sheet_to_csv(csv, { header: 1 });
                return csv;
            } catch (e) {
                console.error(e);
                return "";
            }
        }
        return gk_fileData[filename] || "";
        }
        </script><!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-i18n="our_collection_title">Our Collection - Panama Hats (Style 3)</title>
    <!-- Tailwind CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- 语言支持 -->
    <script src="assets/js/languages.js"></script>
    <!-- i18next库 -->
    <script src="https://cdn.jsdelivr.net/npm/i18next@21.6.10/i18next.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/i18next-browser-languagedetector@6.1.3/i18nextBrowserLanguageDetector.min.js"></script>
    <!-- 自定义i18n实现 -->
    <script src="assets/js/i18n.js"></script>
    <!-- 立即设置语言 -->
    <script>
        // 立即设置HTML语言属性
        (function() {
            // 检查URL参数中是否有语言设置
            const urlParams = new URLSearchParams(window.location.search);
            const langParam = urlParams.get('lang');

            // 如果URL中有语言参数，使用它；否则使用localStorage中的语言或默认语言
            const savedLanguage = langParam || localStorage.getItem('selectedLanguage') || 'zh';

            // 如果URL中有语言参数，保存到localStorage
            if (langParam) {
                localStorage.setItem('selectedLanguage', langParam);
                console.log('从URL参数设置语言为:', langParam);
            }

            document.documentElement.setAttribute('lang', savedLanguage);
            console.log('立即设置HTML lang属性为:', savedLanguage);

            // 如果是阿拉伯语，设置RTL
            if (savedLanguage === 'ar') {
                document.documentElement.dir = 'rtl';
                document.body.classList.add('rtl');
            } else {
                document.documentElement.dir = 'ltr';
                document.body.classList.remove('rtl');
            }

            // 定义一个全局函数，用于直接应用翻译
            window.applyTranslations = function() {
                const currentLang = localStorage.getItem('selectedLanguage') || 'zh';
                console.log('直接应用翻译，当前语言:', currentLang);

                // 确保translations对象存在
                if (typeof translations === 'undefined' || !translations[currentLang]) {
                    console.error('translations对象不存在或当前语言没有翻译数据');
                    return;
                }

                // 直接应用翻译到所有带有data-i18n属性的元素
                document.querySelectorAll('[data-i18n]').forEach(element => {
                    const key = element.getAttribute('data-i18n');
                    if (translations[currentLang][key]) {
                        element.textContent = translations[currentLang][key];
                        console.log('直接翻译元素:', key, '->', translations[currentLang][key]);
                    } else {
                        console.warn('找不到直接翻译键:', key);
                    }
                });

                // 特别处理"Add to Cart"按钮
                document.querySelectorAll('.add-to-cart span').forEach(span => {
                    if (translations[currentLang]['add_to_cart']) {
                        span.textContent = translations[currentLang]['add_to_cart'];
                        console.log('直接翻译购物车按钮:', translations[currentLang]['add_to_cart']);
                    }
                });
            };

            // 页面加载完成后应用翻译
            document.addEventListener('DOMContentLoaded', function() {
                // 延迟执行，确保所有元素都已加载
                setTimeout(function() {
                    window.applyTranslations();

                    document.querySelectorAll('.product-card .flex-wrap').forEach(t=>t.classList.add('hidden'));
                    document.querySelectorAll('.product-card .flex.justify-between.mt-auto').forEach(b=>{
                        b.classList.remove('pt-4');b.classList.add('pt-2');
                        const c=b.querySelector('.btn-primary');
                        if(c){
                            c.classList.remove('px-4','py-2','text-sm');
                            c.classList.add('px-2','py-1','text-xs');
                            const i=c.querySelector('.mr-2');
                            if(i){i.classList.remove('mr-2');i.classList.add('mr-1');}
                        }
                        const f=b.querySelector('.bg-black');
                        if(f){
                            f.classList.remove('px-3','py-2');
                            f.classList.add('px-2','py-1','text-xs');
                        }
                    });
                }, 100);
            });
        })();
    </script>
    <style>
        :root {
            --primary-color: #4CAF50;
            --primary-hover: #45a049;
            --secondary-color: #f8f9fa;
            --accent-color: #ff9800;
        }

        /* 动态背景样式 */
        .dynamic-background {
            color: #f0f0f0;
            background: #101522;
            background-blend-mode: hard-light;
            background-image: radial-gradient(circle at 20% 20%, #ffcc7066 10%, #ffcc7000 50%),
                             radial-gradient(circle at 80% 80%, #0033ff66 10%, #0033ff00 50%),
                             radial-gradient(ellipse at 35% 70%, #00ff4866 10%, #00ff4800 50%),
                             radial-gradient(ellipse at 70% 35%, #ff005d66 10%, #ff005d00 60%);
            background-size: 250% 250%;
            animation: background-animation 30s infinite;
            position: relative;
        }

        .dynamic-background::after {
            content: "";
            width: 100%;
            height: 100%;
            position: fixed;
            top: 0;
            left: 0;
            backdrop-filter: blur(4px);
            background: radial-gradient(ellipse, #00000000, #000000cc);
            z-index: -1;
            pointer-events: none;
        }

        @keyframes background-animation {
            0% {
                background-position: 5% 0%;
            }
            25% {
                background-position: 20% 80%;
            }
            50% {
                background-position: 96% 100%;
            }
            75% {
                background-position: 80% 10%;
            }
            100% {
                background-position: 5% 0%;
            }
        }

        .product-card {
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.2);
            backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            flex-direction: column;
        }

        .product-card .p-4{padding:.75rem!important}
        .product-card h3{font-size:.875rem!important;line-height:1.25!important}
        .product-card p{font-size:.75rem!important;margin-bottom:.5rem!important}
        .product-card .btn-primary{padding:.375rem .75rem!important;font-size:.75rem!important}
        .product-card .price-tag{font-size:.75rem!important;padding:.125rem .375rem!important}

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .product-image {
            aspect-ratio: 1/1; /* 设置为正方形 */
            height: auto; /* 高度自动 */
            background-size: cover;
            background-position: center;
            transition: transform 0.5s ease;
            cursor: pointer; /* 添加指针样式表明可点击 */
        }

        .product-card:hover .product-image {
            transform: scale(1.05);
        }

        .tag {
            transition: all 0.2s ease;
            background-color: rgba(255, 255, 255, 0.2);
            color: #f0f0f0;
        }

        .tag:hover {
            background-color: var(--primary-color);
            color: white;
            transform: scale(1.05);
        }

        .btn-primary {
            background-color: var(--primary-color);
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background-color: var(--primary-hover);
            transform: translateY(-2px);
        }

        .price-tag {
            background-color: var(--primary-color);
            color: white;
            transform: rotate(-5deg);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: rotate(-5deg) scale(1); }
            50% { transform: rotate(-5deg) scale(1.05); }
            100% { transform: rotate(-5deg) scale(1); }
        }

        .discount-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: #f44336;
            color: white;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
        }

        .loading-spinner {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Cart sidebar styles */
        .cart-sidebar {
            transform: translateX(100%);
            transition: transform 0.3s ease-out;
            background-color: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(10px);
            border-left: 1px solid rgba(255, 255, 255, 0.2);
        }

        .cart-sidebar.open {
            transform: translateX(0);
        }

        .cart-overlay {
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
            background-color: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(3px);
        }

        .cart-overlay.open {
            opacity: 1;
            visibility: visible;
        }

        /* Quantity selector */
        .quantity-selector {
            display: flex;
            align-items: center;
        }

        .quantity-btn {
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(0, 0, 0, 0.5);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
            cursor: pointer;
            font-size: 16px;
            transition: all 0.2s ease;
        }

        .quantity-btn:hover {
            background-color: rgba(0, 0, 0, 0.7);
            border-color: rgba(255, 255, 255, 0.4);
        }

        .quantity-input {
            width: 40px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.3);
            background-color: rgba(0, 0, 0, 0.3);
            color: white;
            margin: 0 5px;
            padding: 5px;
        }

        /* Back button style */
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
        }
        .back-button .btn-back {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background-color: rgba(0, 0, 0, 0.6);
            border-radius: 50%;
            box-shadow: 0 2px 5px rgba(0,0,0,0.3);
            color: #fff;
            text-decoration: none;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        .back-button .btn-back:hover {
            background-color: rgba(0, 0, 0, 0.8);
            transform: scale(1.1);
            border-color: rgba(255, 255, 255, 0.5);
        }

        /* 语言选择器样式 */
        .language-dropdown {
            position: relative;
            display: inline-block;
        }

        .language-dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            background-color: rgba(0, 0, 0, 0.8);
            min-width: 160px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.5);
            z-index: 1000;
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .language-dropdown-content a {
            color: white;
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            transition: all 0.2s ease;
        }

        .language-dropdown-content a:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .language-dropdown:hover .language-dropdown-content {
            display: block;
        }

        .language-btn {
            background-color: transparent;
            color: white;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
        }

        /* RTL支持 */
        body.rtl {
            direction: rtl;
            text-align: right;
        }

        body.rtl .back-button {
            left: auto;
            right: 20px;
        }

        .modal-overlay{position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,.8);display:flex;align-items:center;justify-content:center;z-index:1000;opacity:0;pointer-events:none;transition:opacity .3s ease;backdrop-filter:blur(5px)}
        .modal-overlay.open{opacity:1;pointer-events:auto}
        .product-modal{display:flex;width:90%;max-width:1000px;max-height:80vh;background-color:rgba(30,30,30,.9);border-radius:12px;overflow:hidden;box-shadow:0 10px 30px rgba(0,0,0,.5);border:1px solid rgba(255,255,255,.2);animation:modal-appear .3s ease}
        @keyframes modal-appear{from{transform:scale(.9);opacity:0}to{transform:scale(1);opacity:1}}
        .modal-image-container{flex:2;padding:20px;display:flex;align-items:center;justify-content:center}
        .modal-image{width:100%;height:100%;object-fit:contain;border-radius:8px}
        .modal-content{flex:1;padding:30px;display:flex;flex-direction:column;color:#f0f0f0;border-left:1px solid rgba(255,255,255,.1)}
        .modal-title{font-size:24px;font-weight:700;margin-bottom:15px}
        .modal-price{font-size:22px;font-weight:700;color:var(--primary-color);margin-bottom:20px}
        .modal-description{margin-bottom:20px;line-height:1.6}
        .modal-tags{display:flex;flex-wrap:wrap;gap:8px;margin-bottom:30px}
        .modal-tag{background-color:rgba(255,255,255,.2);color:#f0f0f0;padding:5px 10px;border-radius:4px;font-size:12px;transition:all .2s ease}
        .modal-tag:hover{background-color:var(--primary-color);color:#fff}
        .modal-add-to-cart{margin-top:auto;background-color:var(--primary-color);color:#fff;border:none;padding:12px 20px;border-radius:8px;font-weight:700;cursor:pointer;transition:all .3s ease;display:flex;align-items:center;justify-content:center}
        .modal-add-to-cart:hover{background-color:var(--primary-hover);transform:translateY(-2px)}
        .modal-close{position:absolute;top:15px;right:15px;background-color:rgba(0,0,0,.5);color:#fff;border:none;width:30px;height:30px;border-radius:50%;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .2s ease;border:1px solid rgba(255,255,255,.2)}
        .modal-close:hover{background-color:rgba(0,0,0,.8);transform:scale(1.1)}
        @media (max-width:768px){.product-modal{flex-direction:column;max-height:90vh}.modal-image-container{height:50vh;flex:none}.modal-content{flex:none;border-left:none;border-top:1px solid rgba(255,255,255,.1)}}
    </style>
    <!-- 引入通用的图片检测功能 -->
    <script src="assets/js/main.js"></script>
</head>
<body class="antialiased dynamic-background">
    <!-- Back button -->
    <div class="back-button">
        <a href="./PanamaCap.html" class="btn-back">
            <i class="fas fa-arrow-left"></i>
        </a>
    </div>

    <!-- Minimal Header -->
    <header class="bg-black bg-opacity-70 shadow-sm py-4 backdrop-filter backdrop-blur-md">
        <div class="container mx-auto px-4 flex justify-between items-center">
            <h1 class="text-xl font-bold text-white" data-i18n="panama_hats_collection">Panama Hats Collection - Style 3</h1>
            <div class="flex items-center space-x-4">
                <a href="#" class="text-gray-300 hover:text-white">
                    <i class="fas fa-search"></i>
                </a>
                <button id="cartButton" class="text-gray-300 hover:text-white relative">
                    <i class="fas fa-shopping-cart"></i>
                    <span id="cartCount" class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">0</span>
                </button>
            </div>
        </div>
    </header>

    <!-- Products Section -->
    <section class="py-8">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center mb-8">
                <h2 class="text-2xl font-bold text-white" data-i18n="our_collection_title">Our Collection</h2>
                <div class="flex space-x-2">
                    <button class="px-4 py-2 bg-black bg-opacity-50 text-white rounded-lg shadow-sm hover:bg-opacity-70 transition"><span data-i18n="filter">Filter</span> <i class="fas fa-filter ml-2"></i></button>
                    <button class="px-4 py-2 bg-black bg-opacity-50 text-white rounded-lg shadow-sm hover:bg-opacity-70 transition"><span data-i18n="sort">Sort</span> <i class="fas fa-sort ml-2"></i></button>
                </div>
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                <!-- Product 1 -->
                <div class="product-card animate__animated animate__fadeIn" data-id="1" data-name="Panama Hat Style 3 - 1" data-price="89.99" data-image="assets/img/cap/panama/list3/1.jpg">
                    <div class="relative overflow-hidden">
                        <div class="product-image" data-image-base="assets/img/cap/panama/list3/1" style="background-image: url('assets/img/cap/panama/list3/1.jpg');"></div>
                        <div class="discount-badge px-2 py-1 rounded text-xs font-bold" data-i18n="discount_badge">-20%</div>
                    </div>
                    <div class="p-4 flex-grow flex flex-col">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-lg font-semibold" data-i18n="product_1_title">Panama Hat Style 3 - 1</h3>
                            <div class="price-tag px-2 py-1 rounded text-sm font-bold">$89.99</div>
                        </div>
                        <p class="text-gray-200 text-sm mb-3" data-i18n="product_1_desc">Luxury Panama hat with unique design and exceptional comfort.</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_1_1">Luxury</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_1_2">Exclusive</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_1_3">Elegant</span>
                        </div>
                        <div class="flex justify-between mt-auto pt-4">
                            <button class="btn-primary px-4 py-2 rounded-lg text-sm font-semibold flex items-center add-to-cart">
                                <i class="fas fa-shopping-cart mr-2"></i> <span data-i18n="add_to_cart">Add to Cart</span>
                            </button>
                            <button class="bg-black bg-opacity-50 hover:bg-opacity-70 text-white px-3 py-2 rounded-lg transition border border-gray-600">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Product 2 -->
                <div class="product-card animate__animated animate__fadeIn" data-id="2" data-name="Panama Hat Style 3 - 2" data-price="109.99" data-image="assets/img/cap/panama/list3/2.jpg">
                    <div class="relative overflow-hidden">
                        <div class="product-image" data-image-base="assets/img/cap/panama/list3/2" style="background-image: url('assets/img/cap/panama/list3/2.jpg');"></div>
                    </div>
                    <div class="p-4 flex-grow flex flex-col">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-lg font-semibold" data-i18n="product_2_title">Panama Hat Style 3 - 2</h3>
                            <div class="price-tag px-2 py-1 rounded text-sm font-bold">$109.99</div>
                        </div>
                        <p class="text-gray-200 text-sm mb-3" data-i18n="product_2_desc">Sophisticated style for formal occasions.</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_2_1">Formal</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_2_2">Elegant</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_2_3">Classic</span>
                        </div>
                        <div class="flex justify-between mt-auto pt-4">
                            <button class="btn-primary px-4 py-2 rounded-lg text-sm font-semibold flex items-center add-to-cart">
                                <i class="fas fa-shopping-cart mr-2"></i> <span data-i18n="add_to_cart">Add to Cart</span>
                            </button>
                            <button class="bg-black bg-opacity-50 hover:bg-opacity-70 text-white px-3 py-2 rounded-lg transition border border-gray-600">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Product 3 -->
                <div class="product-card animate__animated animate__fadeIn" data-id="3" data-name="Panama Hat Style 3 - 3" data-price="79.99" data-image="assets/img/cap/panama/list3/3.jpg">
                    <div class="relative overflow-hidden">
                        <div class="product-image" data-image-base="assets/img/cap/panama/list3/3" style="background-image: url('assets/img/cap/panama/list3/3.jpg');"></div>
                        <div class="discount-badge px-2 py-1 rounded text-xs font-bold" data-i18n="discount_badge_3">-15%</div>
                    </div>
                    <div class="p-4 flex-grow flex flex-col">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-lg font-semibold" data-i18n="product_3_title">Panama Hat Style 3 - 3</h3>
                            <div class="price-tag px-2 py-1 rounded text-sm font-bold">$79.99</div>
                        </div>
                        <p class="text-gray-200 text-sm mb-3" data-i18n="product_3_desc">Lightweight and breathable for summer days.</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_3_1">Summer</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_3_2">Lightweight</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_3_3">Breathable</span>
                        </div>
                        <div class="flex justify-between mt-auto pt-4">
                            <button class="btn-primary px-4 py-2 rounded-lg text-sm font-semibold flex items-center add-to-cart">
                                <i class="fas fa-shopping-cart mr-2"></i> <span data-i18n="add_to_cart">Add to Cart</span>
                            </button>
                            <button class="bg-black bg-opacity-50 hover:bg-opacity-70 text-white px-3 py-2 rounded-lg transition border border-gray-600">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Product 4 -->
                <div class="product-card animate__animated animate__fadeIn" data-id="4" data-name="Panama Hat Style 3 - 4" data-price="95.99" data-image="assets/img/cap/panama/list3/4.jpg">
                    <div class="relative overflow-hidden">
                        <div class="product-image" data-image-base="assets/img/cap/panama/list3/4" style="background-image: url('assets/img/cap/panama/list3/4.jpg');"></div>
                        <div class="discount-badge px-2 py-1 rounded text-xs font-bold" data-i18n="discount_badge_4">-25%</div>
                    </div>
                    <div class="p-4 flex-grow flex flex-col">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-lg font-semibold" data-i18n="product_4_title">Panama Hat Style 3 - 4</h3>
                            <div class="price-tag px-2 py-1 rounded text-sm font-bold">$95.99</div>
                        </div>
                        <p class="text-gray-200 text-sm mb-3" data-i18n="product_4_desc">Versatile hat for any occasion.</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_4_1">Versatile</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_4_2">Comfortable</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_4_3">Durable</span>
                        </div>
                        <div class="flex justify-between mt-auto pt-4">
                            <button class="btn-primary px-4 py-2 rounded-lg text-sm font-semibold flex items-center add-to-cart">
                                <i class="fas fa-shopping-cart mr-2"></i> <span data-i18n="add_to_cart">Add to Cart</span>
                            </button>
                            <button class="bg-black bg-opacity-50 hover:bg-opacity-70 text-white px-3 py-2 rounded-lg transition border border-gray-600">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Product 5 -->
                <div class="product-card animate__animated animate__fadeIn" data-id="5" data-name="Panama Hat Style 3 - 5" data-price="119.99" data-image="assets/img/cap/panama/list3/5.jpg">
                    <div class="relative overflow-hidden">
                        <div class="product-image" data-image-base="assets/img/cap/panama/list3/5" style="background-image: url('assets/img/cap/panama/list3/5.jpg');"></div>
                        <div class="discount-badge px-2 py-1 rounded text-xs font-bold" data-i18n="discount_badge_5">-10%</div>
                    </div>
                    <div class="p-4 flex-grow flex flex-col">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-lg font-semibold" data-i18n="product_5_title">Panama Hat Style 3 - 5</h3>
                            <div class="price-tag px-2 py-1 rounded text-sm font-bold">$119.99</div>
                        </div>
                        <p class="text-gray-200 text-sm mb-3" data-i18n="product_5_desc">Classic nautical style with modern comfort.</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_5_1">Nautical</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_5_2">Classic</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_5_3">Modern</span>
                        </div>
                        <div class="flex justify-between mt-auto pt-4">
                            <button class="btn-primary px-4 py-2 rounded-lg text-sm font-semibold flex items-center add-to-cart">
                                <i class="fas fa-shopping-cart mr-2"></i> <span data-i18n="add_to_cart">Add to Cart</span>
                            </button>
                            <button class="bg-black bg-opacity-50 hover:bg-opacity-70 text-white px-3 py-2 rounded-lg transition border border-gray-600">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Product 6 -->
                <div class="product-card animate__animated animate__fadeIn" data-id="6" data-name="Panama Hat Style 3 - 6" data-price="129.99" data-image="assets/img/cap/panama/list3/6.jpg">
                    <div class="relative overflow-hidden">
                        <div class="product-image" data-image-base="assets/img/cap/panama/list3/6" style="background-image: url('assets/img/cap/panama/list3/6.jpg');"></div>
                    </div>
                    <div class="p-4 flex-grow flex flex-col">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-lg font-semibold" data-i18n="product_6_title">Panama Hat Style 3 - 6</h3>
                            <div class="price-tag px-2 py-1 rounded text-sm font-bold">$129.99</div>
                        </div>
                        <p class="text-gray-200 text-sm mb-3" data-i18n="product_6_desc">Premium quality with traditional craftsmanship.</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_6_1">Premium</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_6_2">Traditional</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_6_3">Handcrafted</span>
                        </div>
                        <div class="flex justify-between mt-auto pt-4">
                            <button class="btn-primary px-4 py-2 rounded-lg text-sm font-semibold flex items-center add-to-cart">
                                <i class="fas fa-shopping-cart mr-2"></i> <span data-i18n="add_to_cart">Add to Cart</span>
                            </button>
                            <button class="bg-black bg-opacity-50 hover:bg-opacity-70 text-white px-3 py-2 rounded-lg transition border border-gray-600">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Product 7 -->
                <div class="product-card animate__animated animate__fadeIn" data-id="7" data-name="Panama Hat Style 3 - 7" data-price="69.99" data-image="assets/img/cap/panama/list3/7.jpg">
                    <div class="relative overflow-hidden">
                        <div class="product-image" data-image-base="assets/img/cap/panama/list3/7" style="background-image: url('assets/img/cap/panama/list3/7.jpg');"></div>
                    </div>
                    <div class="p-4 flex-grow flex flex-col">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-lg font-semibold" data-i18n="product_7_title">Panama Hat Style 3 - 7</h3>
                            <div class="price-tag px-2 py-1 rounded text-sm font-bold">$69.99</div>
                        </div>
                        <p class="text-gray-200 text-sm mb-3" data-i18n="product_7_desc">Everyday comfort with timeless style.</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_7_1">Comfortable</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_7_2">Timeless</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_7_3">Everyday</span>
                        </div>
                        <div class="flex justify-between mt-auto pt-4">
                            <button class="btn-primary px-4 py-2 rounded-lg text-sm font-semibold flex items-center add-to-cart">
                                <i class="fas fa-shopping-cart mr-2"></i> <span data-i18n="add_to_cart">Add to Cart</span>
                            </button>
                            <button class="bg-black bg-opacity-50 hover:bg-opacity-70 text-white px-3 py-2 rounded-lg transition border border-gray-600">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Product 8 -->
                <div class="product-card animate__animated animate__fadeIn" data-id="8" data-name="Panama Hat Style 3 - 8" data-price="99.99" data-image="assets/img/cap/panama/list3/8.jpg">
                    <div class="relative overflow-hidden">
                        <div class="product-image" data-image-base="assets/img/cap/panama/list3/8" style="background-image: url('assets/img/cap/panama/list3/8.jpg');"></div>
                    </div>
                    <div class="p-4 flex-grow flex flex-col">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-lg font-semibold" data-i18n="product_8_title">Panama Hat Style 3 - 8</h3>
                            <div class="price-tag px-2 py-1 rounded text-sm font-bold">$99.99</div>
                        </div>
                        <p class="text-gray-200 text-sm mb-3" data-i18n="product_8_desc">Stylish design for the modern gentleman.</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_8_1">Modern</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_8_2">Stylish</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_8_3">Gentleman</span>
                        </div>
                        <div class="flex justify-between mt-auto pt-4">
                            <button class="btn-primary px-4 py-2 rounded-lg text-sm font-semibold flex items-center add-to-cart">
                                <i class="fas fa-shopping-cart mr-2"></i> <span data-i18n="add_to_cart">Add to Cart</span>
                            </button>
                            <button class="bg-black bg-opacity-50 hover:bg-opacity-70 text-white px-3 py-2 rounded-lg transition border border-gray-600">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Product 9 -->
                <div class="product-card animate__animated animate__fadeIn" data-id="9" data-name="Panama Hat Style 3 - 9" data-price="139.99" data-image="assets/img/cap/panama/list3/9.jpg">
                    <div class="relative overflow-hidden">
                        <div class="product-image" data-image-base="assets/img/cap/panama/list3/9" style="background-image: url('assets/img/cap/panama/list3/9.jpg');"></div>
                        <div class="discount-badge px-2 py-1 rounded text-xs font-bold" data-i18n="discount_badge_9">-15%</div>
                    </div>
                    <div class="p-4 flex-grow flex flex-col">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-lg font-semibold" data-i18n="product_9_title">Panama Hat Style 3 - 9</h3>
                            <div class="price-tag px-2 py-1 rounded text-sm font-bold">$139.99</div>
                        </div>
                        <p class="text-gray-200 text-sm mb-3" data-i18n="product_9_desc">Luxury design with premium materials.</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_9_1">Luxury</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_9_2">Premium</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_9_3">Exclusive</span>
                        </div>
                        <div class="flex justify-between mt-auto pt-4">
                            <button class="btn-primary px-4 py-2 rounded-lg text-sm font-semibold flex items-center add-to-cart">
                                <i class="fas fa-shopping-cart mr-2"></i> <span data-i18n="add_to_cart">Add to Cart</span>
                            </button>
                            <button class="bg-black bg-opacity-50 hover:bg-opacity-70 text-white px-3 py-2 rounded-lg transition border border-gray-600">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Product 10 -->
                <div class="product-card animate__animated animate__fadeIn" data-id="10" data-name="Panama Hat Style 3 - 10" data-price="89.99" data-image="assets/img/cap/panama/list3/10.jpg">
                    <div class="relative overflow-hidden">
                        <div class="product-image" data-image-base="assets/img/cap/panama/list3/10" style="background-image: url('assets/img/cap/panama/list3/10.jpg');"></div>
                    </div>
                    <div class="p-4 flex-grow flex flex-col">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-lg font-semibold" data-i18n="product_10_title">Panama Hat Style 3 - 10</h3>
                            <div class="price-tag px-2 py-1 rounded text-sm font-bold">$89.99</div>
                        </div>
                        <p class="text-gray-200 text-sm mb-3" data-i18n="product_10_desc">Casual comfort for everyday wear.</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_10_1">Casual</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_10_2">Comfortable</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_10_3">Everyday</span>
                        </div>
                        <div class="flex justify-between mt-auto pt-4">
                            <button class="btn-primary px-4 py-2 rounded-lg text-sm font-semibold flex items-center add-to-cart">
                                <i class="fas fa-shopping-cart mr-2"></i> <span data-i18n="add_to_cart">Add to Cart</span>
                            </button>
                            <button class="bg-black bg-opacity-50 hover:bg-opacity-70 text-white px-3 py-2 rounded-lg transition border border-gray-600">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Product 11 -->
                <div class="product-card animate__animated animate__fadeIn" data-id="11" data-name="Panama Hat Style 3 - 11" data-price="109.99" data-image="assets/img/cap/panama/list3/11.jpg">
                    <div class="relative overflow-hidden">
                        <div class="product-image" data-image-base="assets/img/cap/panama/list3/11" style="background-image: url('assets/img/cap/panama/list3/11.jpg');"></div>
                        <div class="discount-badge px-2 py-1 rounded text-xs font-bold" data-i18n="discount_badge_11">-20%</div>
                    </div>
                    <div class="p-4 flex-grow flex flex-col">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-lg font-semibold" data-i18n="product_11_title">Panama Hat Style 3 - 11</h3>
                            <div class="price-tag px-2 py-1 rounded text-sm font-bold">$109.99</div>
                        </div>
                        <p class="text-gray-200 text-sm mb-3" data-i18n="product_11_desc">Elegant design for special occasions.</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_11_1">Elegant</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_11_2">Special</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_11_3">Formal</span>
                        </div>
                        <div class="flex justify-between mt-auto pt-4">
                            <button class="btn-primary px-4 py-2 rounded-lg text-sm font-semibold flex items-center add-to-cart">
                                <i class="fas fa-shopping-cart mr-2"></i> <span data-i18n="add_to_cart">Add to Cart</span>
                            </button>
                            <button class="bg-black bg-opacity-50 hover:bg-opacity-70 text-white px-3 py-2 rounded-lg transition border border-gray-600">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Product 12 -->
                <div class="product-card animate__animated animate__fadeIn" data-id="12" data-name="Panama Hat Style 3 - 12" data-price="149.99" data-image="assets/img/cap/panama/list3/12.jpg">
                    <div class="relative overflow-hidden">
                        <div class="product-image" data-image-base="assets/img/cap/panama/list3/12" style="background-image: url('assets/img/cap/panama/list3/12.jpg');"></div>
                    </div>
                    <div class="p-4 flex-grow flex flex-col">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-lg font-semibold" data-i18n="product_12_title">Panama Hat Style 3 - 12</h3>
                            <div class="price-tag px-2 py-1 rounded text-sm font-bold">$149.99</div>
                        </div>
                        <p class="text-gray-200 text-sm mb-3" data-i18n="product_12_desc">Bold style for modern explorers.</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_12_1">Bold</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_12_2">Modern</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_12_3">Explorer</span>
                        </div>
                        <div class="flex justify-between mt-auto pt-4">
                            <button class="btn-primary px-4 py-2 rounded-lg text-sm font-semibold flex items-center add-to-cart">
                                <i class="fas fa-shopping-cart mr-2"></i> <span data-i18n="add_to_cart">Add to Cart</span>
                            </button>
                            <button class="bg-black bg-opacity-50 hover:bg-opacity-70 text-white px-3 py-2 rounded-lg transition border border-gray-600">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-8">
                <button id="loadMore" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-full font-semibold shadow-lg hover:shadow-xl transition">
                    <span data-i18n="load_more">Load More</span> <i id="spinner" class="fas fa-spinner loading-spinner ml-2 hidden"></i>
                </button>
            </div>
        </div>
    </section>

    <!-- 购物车侧边栏 -->
    <div class="cart-sidebar fixed top-0 right-0 w-96 h-full z-50 p-6 text-white" id="cartSidebar">
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-xl font-bold" data-i18n="your_cart">Your Cart</h2>
            <button id="closeCart" class="text-gray-300 hover:text-white">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div id="cartItems" class="mb-6">
            <!-- 购物车项目将通过JavaScript动态生成 -->
        </div>
        <div id="cartEmpty" class="text-center py-8 text-gray-400" data-i18n="cart_empty">Your cart is empty</div>
        <div id="cartSummary" class="border-t border-gray-700 pt-4 hidden">
            <div class="flex justify-between mb-2">
                <span data-i18n="subtotal">Subtotal</span>
                <span id="cartSubtotal">$0.00</span>
            </div>
            <div class="flex justify-between mb-2">
                <span data-i18n="shipping">Shipping</span>
                <span data-i18n="free">Free</span>
            </div>
            <div class="flex justify-between font-bold text-lg mb-4">
                <span data-i18n="total">Total</span>
                <span id="cartTotal">$0.00</span>
            </div>
            <button id="checkoutBtn" class="w-full py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-all" data-i18n="proceed_to_checkout">Proceed to Checkout</button>
        </div>
    </div>
    <div class="cart-overlay fixed inset-0 z-40" id="cartOverlay"></div>

    <!-- 产品模态框 -->
    <div class="modal-overlay" id="productModal">
        <div class="product-modal">
            <button class="modal-close" id="modalClose">
                <i class="fas fa-times"></i>
            </button>
            <div class="modal-image-container">
                <img id="modalImage" src="" alt="Product" class="modal-image">
            </div>
            <div class="modal-content">
                <h2 id="modalTitle" class="modal-title"></h2>
                <p id="modalPrice" class="modal-price"></p>
                <div id="modalTags" class="modal-tags"></div>
                <p id="modalDescription" class="modal-description"></p>
                <button id="modalAddToCart" class="modal-add-to-cart">
                    <i class="fas fa-shopping-cart mr-2"></i>
                    <span data-i18n="add_to_cart">Add to Cart</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Updated Footer with Black Banner -->
    <footer style="position: fixed; bottom: 0; left: 0; right: 0; background-color: #000000; padding: 10px 0; text-align: center; color: #ffffff;">
        <p data-i18n="footer_copyright_text">2023 Panama Hats Collection. All rights reserved.</p>
    </footer>

    <script type="text/javascript">
        var gk_isXlsx = false;
        var gk_xlsxFileLookup = {};
        var gk_fileData = {};
        function filledCell(cell) {
            return cell !== '' && cell != null;
        }
        function loadFileData(filename) {
            if (gk_isXlsx && gk_xlsxFileLookup[filename]) {
                try {
                    var workbook = XLSX.read(gk_fileData[filename], { type: 'base64' });
                    var firstSheetName = workbook.SheetNames[0];
                    var worksheet = workbook.Sheets[firstSheetName];

                    // Convert sheet to JSON to filter blank rows
                    var jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, blankrows: false, defval: '' });
                    // Filter out blank rows (rows where all cells are empty, null, or undefined)
                    var filteredData = jsonData.filter(row => row.some(filledCell));

                    // Heuristic to find the header row by ignoring rows with fewer filled cells than the next row
                    var headerRowIndex = filteredData.findIndex((row, index) =>
                        row.filter(filledCell).length >= filteredData[index + 1]?.filter(filledCell).length
                    );
                    // Fallback
                    if (headerRowIndex === -1 || headerRowIndex > 25) {
                        headerRowIndex = 0;
                    }

                    // Convert filtered JSON back to CSV
                    var csv = XLSX.utils.aoa_to_sheet(filteredData.slice(headerRowIndex)); // Create a new sheet from filtered array of arrays
                    csv = XLSX.utils.sheet_to_csv(csv, { header: 1 });
                    return csv;
                } catch (e) {
                    console.error(e);
                    return "";
                }
            }
            return gk_fileData[filename] || "";
        }
    </script>

    <script>
        // Cart functionality
        let cart = [];
        const cartButton = document.getElementById('cartButton');
        const cartCount = document.getElementById('cartCount');
        const cartSidebar = document.getElementById('cartSidebar');
        const cartOverlay = document.getElementById('cartOverlay');
        const closeCart = document.getElementById('closeCart');
        const cartItems = document.getElementById('cartItems');
        const cartSubtotal = document.getElementById('cartSubtotal');
        const cartTotal = document.getElementById('cartTotal');
        const checkoutBtn = document.getElementById('checkoutBtn');

        // 产品详情悬浮图框元素
        const productModal = document.getElementById('productModal');
        const modalClose = document.getElementById('modalClose');
        const modalImage = document.getElementById('modalImage');
        const modalTitle = document.getElementById('modalTitle');
        const modalPrice = document.getElementById('modalPrice');
        const modalDescription = document.getElementById('modalDescription');
        const modalTags = document.getElementById('modalTags');
        const modalAddToCart = document.getElementById('modalAddToCart');

        // 从localStorage加载购物车数据
        function loadCart() {
            const savedCart = localStorage.getItem('panamaCap_cart');
            if (savedCart) {
                try {
                    cart = JSON.parse(savedCart);
                    console.log('从localStorage加载购物车数据:', cart.length, '个商品');
                    updateCart();
                } catch (e) {
                    console.error('解析购物车数据出错:', e);
                    cart = [];
                }
            }
        }

        // 保存购物车数据到localStorage
        function saveCart() {
            localStorage.setItem('panamaCap_cart', JSON.stringify(cart));
            console.log('购物车数据已保存，共', cart.length, '个商品');
        }

        // Toggle cart visibility
        cartButton.addEventListener('click', () => {
            // 确保购物车数据是最新的
            loadCart();

            // 检测并设置所有产品图片
            if (typeof window.initializeProductImages === 'function') {
                window.initializeProductImages();
            } else {
                console.error('图片检测功能未加载');
            }
            // 打开购物车侧边栏
            cartSidebar.classList.add('open');
            cartOverlay.classList.add('open');
        });

        closeCart.addEventListener('click', () => {
            cartSidebar.classList.remove('open');
            cartOverlay.classList.remove('open');
        });

        cartOverlay.addEventListener('click', () => {
            cartSidebar.classList.remove('open');
            cartOverlay.classList.remove('open');
            productModal.classList.remove('open');
        });

        // 更新购物车UI
        function updateCart() {
            // 更新购物车计数
            const totalItems = cart.reduce((total, item) => total + item.quantity, 0);
            cartCount.textContent = totalItems;

            // 更新购物车项目
            cartItems.innerHTML = '';

            if (cart.length === 0) {
                cartItems.innerHTML = `
                    <div class="text-center py-8 text-gray-500">
                        <i class="fas fa-shopping-cart text-4xl mb-2"></i>
                        <p data-i18n="cart_empty">Your cart is empty</p>
                    </div>
                `;
                checkoutBtn.classList.add('disabled');
                checkoutBtn.style.pointerEvents = 'none';
                cartSubtotal.textContent = '$0.00';
                cartTotal.textContent = '$0.00';
                return;
            }

            checkoutBtn.classList.remove('disabled');
            checkoutBtn.style.pointerEvents = 'auto';

            let subtotal = 0;

            cart.forEach(item => {
                const itemTotal = item.price * item.quantity;
                subtotal += itemTotal;

                const cartItem = document.createElement('div');
                cartItem.className = 'flex items-center justify-between mb-4 pb-4 border-b border-gray-700';
                cartItem.innerHTML = `
                    <div class="flex items-center">
                        <img src="${item.image}" alt="${item.name}" class="w-16 h-16 object-cover rounded mr-4">
                        <div>
                            <h3 class="font-medium text-white">${item.name}</h3>
                            <p class="text-gray-400">$${item.price.toFixed(2)} x ${item.quantity}</p>
                            <div class="quantity-selector mt-2">
                                <button class="quantity-btn decrease" data-id="${item.id}">-</button>
                                <input type="number" class="quantity-input" value="${item.quantity}" min="1" max="99" readonly>
                                <button class="quantity-btn increase" data-id="${item.id}">+</button>
                            </div>
                        </div>
                    </div>
                    <button class="text-gray-400 hover:text-red-500 remove-item" data-id="${item.id}">
                        <i class="fas fa-trash"></i>
                    </button>
                `;

                cartItems.appendChild(cartItem);
            });

            // 更新小计和总计
            cartSubtotal.textContent = '$' + subtotal.toFixed(2);
            cartTotal.textContent = '$' + subtotal.toFixed(2);

            // 添加数量调整和移除项目事件
            document.querySelectorAll('.quantity-btn.decrease').forEach(button => {
                button.addEventListener('click', function() {
                    const id = this.dataset.id;
                    const item = cart.find(item => item.id === id);
                    if (item && item.quantity > 1) {
                        item.quantity -= 1;
                        updateCart();
                        saveCart();
                    }
                });
            });

            document.querySelectorAll('.quantity-btn.increase').forEach(button => {
                button.addEventListener('click', function() {
                    const id = this.dataset.id;
                    const item = cart.find(item => item.id === id);
                    if (item) {
                        item.quantity += 1;
                        updateCart();
                        saveCart();
                    }
                });
            });

            document.querySelectorAll('.remove-item').forEach(button => {
                button.addEventListener('click', function() {
                    const id = this.dataset.id;
                    cart = cart.filter(item => item.id !== id);
                    updateCart();
                    saveCart();
                });
            });
        }

        // 跟踪当前产品
        let currentProduct = null;

        // 初始化产品交互
        function initializeProducts() {
            // 添加产品点击事件
            document.querySelectorAll('.product-image').forEach(img => {
                img.addEventListener('click', async function() {
                    const card = this.closest('.product-card');
                    const id = card.dataset.id;
                    const name = card.dataset.name;
                    const price = card.dataset.price;
                    const image = card.dataset.image;
                    const desc = card.querySelector('[data-i18n^="product_"][data-i18n$="_desc"]');
                    const tags = card.querySelectorAll('.tag');

                    // 获取图片的基础路径（去掉扩展名）
                    const basePath = image.replace(/\.[^/.]+$/, "");

                    // 清理之前的内容（缩略图和视频）
                    const existingThumbnails = productModal.querySelector('.sub-images-container');
                    if (existingThumbnails) {
                        existingThumbnails.remove();
                        console.log('已清理之前的缩略图容器');
                    }

                    // 清理可能存在的视频元素
                    const modalImageContainer = productModal.querySelector('.modal-image-container');
                    if (modalImageContainer) {
                        const existingVideos = modalImageContainer.querySelectorAll('video');
                        existingVideos.forEach(video => {
                            console.log('清理之前的视频元素:', video.src);
                            video.pause();
                            video.currentTime = 0;
                            video.src = '';
                            video.remove();
                        });
                        if (existingVideos.length > 0) {
                            console.log(`已清理 ${existingVideos.length} 个之前的视频元素`);
                        }
                    }

                    // 使用动态图片检测功能
                    let detectedImageUrl = image; // 默认使用原始路径
                    if (typeof window.detectModalImage === 'function') {
                        try {
                            detectedImageUrl = await window.detectModalImage(basePath);
                            console.log('模态框检测到的图片路径:', detectedImageUrl);
                        } catch (error) {
                            console.error('模态框图片检测出错:', error);
                        }
                    } else {
                        console.warn('模态框图片检测功能未加载，使用默认路径');
                    }

                    // 检测子图片
                    let subImages = [];
                    if (typeof window.detectSubImages === 'function') {
                        try {
                            subImages = await window.detectSubImages(basePath);
                            console.log(`检测到 ${subImages.length} 个子图片`);
                        } catch (error) {
                            console.error('子图片检测出错:', error);
                        }
                    }

                    // 保存当前产品信息
                    currentProduct = {id, name, price, image: detectedImageUrl};

                    // 设置模态框内容
                    modalImage.src = detectedImageUrl;
                    modalTitle.textContent = name;
                    modalPrice.textContent = '$' + price;
                    if (desc) modalDescription.textContent = desc.textContent;

                    // 清空并添加标签
                    modalTags.innerHTML = '';
                    tags.forEach(tag => {
                        const t = document.createElement('span');
                        t.className = 'modal-tag';
                        t.textContent = tag.textContent;
                        modalTags.appendChild(t);
                    });

                    // 如果有子图片，创建缩略图
                    if (subImages.length > 0) {
                        console.log(`创建 ${subImages.length} 个缩略图`);
                        const modalContainer = productModal.querySelector('.product-modal') || productModal;
                        if (typeof window.createSubImageThumbnails === 'function') {
                            window.createSubImageThumbnails(subImages, modalImage, modalContainer);
                        }
                    }

                    // 显示模态框
                    productModal.classList.add('open');
                    document.body.style.overflow = 'hidden'; // 防止背景滚动
                });
            });

            // 模态框添加到购物车
            modalAddToCart.addEventListener('click', function() {
                if (currentProduct) {
                    // 检查购物车中是否已有该商品
                    const item = cart.find(i => i.id === currentProduct.id);
                    if (item) {
                        item.quantity += 1;
                    } else {
                        cart.push({
                            id: currentProduct.id,
                            name: currentProduct.name,
                            price: parseFloat(currentProduct.price),
                            image: currentProduct.image,
                            quantity: 1
                        });
                    }

                    // 更新购物车UI并保存
                    updateCart();
                    saveCart();

                    // 显示添加成功提示
                    const txt = this.innerHTML;
                    this.innerHTML = '<i class="fas fa-check mr-2"></i> 已添加到购物车!';
                    this.style.backgroundColor = '#28a745';

                    setTimeout(() => {
                        this.innerHTML = txt;
                        this.style.backgroundColor = '';
                    }, 1500);
                }
            });

            // 添加到购物车按钮
            document.querySelectorAll('.add-to-cart').forEach(btn => {
                btn.addEventListener('click', function() {
                    const card = this.closest('.product-card');
                    const id = card.dataset.id;
                    const name = card.dataset.name;
                    const price = parseFloat(card.dataset.price);
                    const image = card.dataset.image;

                    // 检查购物车中是否已有该商品
                    const existingItem = cart.find(item => item.id === id);
                    if (existingItem) {
                        existingItem.quantity += 1;
                    } else {
                        cart.push({
                            id,
                            name,
                            price,
                            image,
                            quantity: 1
                        });
                    }

                    // 更新购物车UI并保存
                    updateCart();
                    saveCart();

                    // 打开购物车侧边栏
                    cartSidebar.classList.add('open');
                    cartOverlay.classList.add('open');

                    // 显示添加成功提示
                    const added = document.createElement('span');
                    added.textContent = 'Added!';
                    added.className = 'text-green-600 ml-2';
                    this.appendChild(added);

                    setTimeout(() => {
                        if (added.parentNode === this) {
                            this.removeChild(added);
                        }
                    }, 1000);
                });
            });
        }

        // 清理模态框的函数
        function cleanupModal() {
            // 移除之前的缩略图容器
            const existingThumbnails = productModal.querySelector('.sub-images-container');
            if (existingThumbnails) {
                existingThumbnails.remove();
                console.log('已清理之前的缩略图容器');
            }

            // 清理可能存在的视频元素
            const modalImageContainer = productModal.querySelector('.modal-image-container');
            if (modalImageContainer) {
                // 查找并移除所有视频元素
                const existingVideos = modalImageContainer.querySelectorAll('video');
                existingVideos.forEach(video => {
                    console.log('清理视频元素:', video.src);
                    // 停止视频播放
                    video.pause();
                    video.currentTime = 0;
                    video.src = '';
                    // 移除视频元素
                    video.remove();
                });

                if (existingVideos.length > 0) {
                    console.log(`已清理 ${existingVideos.length} 个视频元素`);
                }
            }

            // 重置模态框图片状态
            if (modalImage) {
                modalImage.style.opacity = '1';
                modalImage.style.transition = '';
                modalImage.style.position = '';
                modalImage.style.zIndex = '';
            }

            productModal.classList.remove('open');
            document.body.style.overflow = ''; // 恢复背景滚动
            currentProduct = null;
        }

        // 关闭模态框
        modalClose.addEventListener('click', cleanupModal);

        // 点击模态框背景关闭
        productModal.addEventListener('click', e => {
            if (e.target === productModal) {
                cleanupModal();
            }
        });

        // 加载更多按钮点击事件
        document.getElementById('loadMore').addEventListener('click', async function() {
            try {
                console.log('用户点击加载更多按钮');
                const result = await window.loadMoreProducts('assets/img/cap/panama/list3');
                console.log('加载更多产品结果:', result);
            } catch (error) {
                console.error('加载更多产品失败:', error);
            }
        });

        // 监听自定义事件来处理新产品的模态框显示
        document.addEventListener('showProductModal', async function(event) {
            const { id, name, price, image, description, tags, basePath, subImages } = event.detail;
            currentProduct = { id, name, price, image };
            modalImage.src = image;
            modalTitle.textContent = name;
            modalPrice.textContent = '$' + price;
            modalDescription.textContent = description;

            modalTags.innerHTML = '';
            tags.forEach(tagText => {
                const tag = document.createElement('span');
                tag.className = 'modal-tag';
                tag.textContent = tagText;
                modalTags.appendChild(tag);
            });

            if (subImages && subImages.length > 0) {
                console.log(`为新产品创建 ${subImages.length} 个缩略图`);
                const modalContainer = productModal.querySelector('.product-modal') || productModal;
                if (typeof window.createSubImageThumbnails === 'function') {
                    window.createSubImageThumbnails(subImages, modalImage, modalContainer);
                }
            }

            productModal.classList.add('open');
            document.body.style.overflow = 'hidden';
        });

        // 监听自定义事件来处理添加到购物车
        document.addEventListener('addToCart', function(event) {
            const { id, name, price, image, quantity } = event.detail;
            const existingItem = cart.find(item => item.id === id);
            if (existingItem) {
                existingItem.quantity += quantity;
            } else {
                cart.push({ id, name, price, image, quantity });
            }
            updateCart();
            saveCart();
            console.log('商品已添加到购物车:', { id, name, price, quantity });
        });

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化产品交互
            initializeProducts();

            // 加载购物车数据
            loadCart();

            // 检测并设置所有产品图片
            if (typeof window.initializeProductImages === 'function') {
                window.initializeProductImages();
            } else {
                console.error('图片检测功能未加载');
            }

            // 重置加载状态
            if (typeof window.resetLoadMoreState === 'function') {
                window.resetLoadMoreState('assets/img/cap/panama/list3');
            }

            // 初始化简单产品网格
            if (typeof window.initializeSimpleGrid === 'function') {
                console.log('初始化简单产品网格');
                window.initializeSimpleGrid('assets/img/cap/panama/list3');
            } else {
                console.error('简单网格初始化功能未加载');
            }

            // 延迟执行图片初始化，确保所有元素都已加载
            setTimeout(function() {
                console.log('开始初始化产品图片');

                // 检测并设置所有产品图片
                console.log('检查图片初始化函数是否可用:', typeof window.initializeProductImages);
                if (typeof window.initializeProductImages === 'function') {
                    console.log('调用图片初始化函数');
                    window.initializeProductImages();
                } else {
                    console.error('图片检测功能未加载，尝试手动设置图片');
                    // 手动设置图片的备用方案
                    manuallySetProductImages();
                }

                // 应用翻译
                if (typeof window.applyTranslations === 'function') {
                    window.applyTranslations();
                }
            }, 200);

            // 延迟执行一次额外的翻译应用，确保所有元素都已加载
            setTimeout(function() {
                console.log('延迟执行额外的翻译应用');
                if (typeof window.applyTranslations === 'function') {
                    window.applyTranslations();
                }
            }, 800);
        });

        // 手动设置产品图片的备用函数
        function manuallySetProductImages() {
            console.log('执行手动图片设置');
            document.querySelectorAll('.product-image[data-image-base]').forEach(function(element, index) {
                const basePath = element.getAttribute('data-image-base');
                console.log('处理图片元素:', basePath);

                if (basePath) {
                    // 直接设置图片路径，优先使用 .jpg 格式
                    const imageUrl = basePath + '.jpg';
                    element.style.backgroundImage = `url('${imageUrl}')`;
                    element.style.backgroundSize = 'cover';
                    element.style.backgroundPosition = 'center';
                    element.style.backgroundRepeat = 'no-repeat';

                    console.log('设置图片:', imageUrl);

                    // 测试图片是否能加载
                    const testImg = new Image();
                    testImg.onload = function() {
                        console.log('图片加载成功:', imageUrl);
                    };
                    testImg.onerror = function() {
                        console.error('图片加载失败:', imageUrl);
                        // 如果 .jpg 失败，尝试其他格式
                        const extensions = ['jpeg', 'png', 'webp'];
                        for (let ext of extensions) {
                            const altUrl = basePath + '.' + ext;
                            const altImg = new Image();
                            altImg.onload = function() {
                                console.log('使用备用格式:', altUrl);
                                element.style.backgroundImage = `url('${altUrl}')`;
                            };
                            altImg.src = altUrl;
                        }
                    };
                    testImg.src = imageUrl;
                }
            });
        }
    </script>
</body>
</html>
