<!DOCTYPE html> <!-- 定义文档类型为 HTML5 -->
<html lang="en"> <!-- 指定页面内容的主要语言为英文 -->
<head>
    <meta charset="UTF-8"> <!-- 设置文档编码为 UTF-8，支持多语言显示 -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0"> <!-- 响应式设计，确保页面在不同设备上正常显示 -->
    <title data-i18n="baseball_caps">Baseball Caps</title> <!-- 设置页面标题 -->
    <!-- 引入外部 CSS 文件 -->
    <link rel="stylesheet" href="./assets/vendor/Panamastyles.css"> <!-- 使用相同的样式表 -->
    <link href="assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <style>
      /* 导航栏样式 */
      .navbar {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        padding: 10px 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        z-index: 1000;
      }
      .navbar a {
        color: white;
        text-decoration: none;
        margin: 0 10px;
      }
      .navbar a:hover {
        text-decoration: underline;
      }
      .back-btn {
        font-size: 24px;
        display: flex;
        align-items: center;
        transition: transform 0.3s ease;
      }
      .back-btn:hover {
        transform: scale(1.2);
        text-decoration: none !important;
      }
      .navbar .nav-links {
        display: flex;
        align-items: center;
      }
      body {
        padding-top: 60px;
      }
      /* 语言选择器样式 */
      .language-dropdown {
        position: relative;
        display: inline-block;
      }
      .language-dropdown-content {
        display: none;
        position: absolute;
        right: 0;
        background-color: #f9f9f9;
        min-width: 160px;
        box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
        z-index: 1;
      }
      .language-dropdown-content a {
        color: black;
        padding: 12px 16px;
        text-decoration: none;
        display: block;
      }
      .language-dropdown-content a:hover {
        background-color: #f1f1f1;
      }
      .language-dropdown:hover .language-dropdown-content {
        display: block;
      }
      .language-btn {
        background-color: transparent;
        color: white;
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
      }

      /* 视频和图片样式 */
      .card-image {
        position: relative;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        min-height: 300px; /* 确保卡片图片区域有足够的高度 */
        z-index: 1; /* 确保图片层级在底部，但高于背景 */
      }

      /* 修复鼠标事件问题 - 让覆盖元素不阻挡视频激活 */
      .card-header {
        pointer-events: none; /* 让鼠标事件穿透header */
        z-index: 10; /* 确保header在视觉上仍然在上层 */
      }

      .card-header h2 {
        pointer-events: auto; /* 恢复标题的鼠标事件 */
      }

      /* 确保其他交互元素的鼠标事件正常 */
      label.open-card-label,
      label.card-toggle-label {
        pointer-events: auto; /* 确保标签按钮可以点击 */
      }
      .card-video {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        opacity: 0;
        transition: opacity 0.3s ease;
        z-index: 1;
      }
      .video-error-message {
        display: none;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        background-color: rgba(0,0,0,0.7);
        padding: 10px;
        border-radius: 5px;
        z-index: 2;
        text-align: center;
        font-size: 14px;
      }
    </style>
</head>
<body>
  <!-- 导航栏 -->
  <div class="navbar">
    <a href="./index.html" class="back-btn"><i class="bi bi-arrow-left-circle-fill"></i></a>
    <div class="nav-links">
      <div class="language-dropdown">
        <button class="language-btn">
          <i class="bi bi-globe"></i> <span id="current-language" class="ms-1">EN</span> <i class="bi bi-chevron-down ms-1"></i>
        </button>
        <div class="language-dropdown-content" id="language-menu">
          <a href="#" data-lang="en">English</a>
          <a href="#" data-lang="zh">中文</a>
          <a href="#" data-lang="es">Español</a>
          <a href="#" data-lang="ar">العربية</a>
          <a href="#" data-lang="fr">Français</a>
          <a href="#" data-lang="ru">Русский</a>
          <a href="#" data-lang="pt">Português</a>
          <a href="#" data-lang="de">Deutsch</a>
          <a href="#" data-lang="ja">日本語</a>
          <a href="#" data-lang="hi">हिन्दी</a>
        </div>
      </div>
    </div>
  </div>

  <div class="card" id="card-1">
    <input type="checkbox" class="open-card" id="open-card-1" checked />
    <label class="open-card-label" for="open-card-1"></label>
    <input type="checkbox" class="card-toggle" id="card-toggle-1" />
    <label class="card-toggle-label" for="card-toggle-1"></label>
    <div class="card-image" style="background-image: url('./assets/img/cap/Baseball/1.avif');">
      <video class="card-video" muted loop></video>
    </div>
    <div class="card-header">
      <h2 data-i18n="method_1">Method 1</h2>
    </div>
    <div class="card-content left-content">
      <h3 data-i18n="baseball_left_title">Classic Baseball Cap</h3>
      <p data-i18n="baseball_left_desc1">Stylish baseball caps with adjustable straps, perfect for casual wear and sports activities.</p>
      <p data-i18n="baseball_left_desc2">Available in various colors and designs to match your personal style and daily outfits.</p>
      <a class="card-button" href="#" data-i18n="baseball_read_more">Read more</a>
    </div>
    <div class="card-content right-content">
      <h3 data-i18n="baseball_right_title">Premium Quality</h3>
      <p data-i18n="baseball_right_desc1">Made from high-quality cotton and polyester materials that ensure durability and comfort during extended wear.</p>
      <p data-i18n="baseball_right_desc2">Breathable fabric and adjustable fit for all-day comfort in any weather condition.</p>
      <a class="card-button" href="#" data-i18n="baseball_read_more">Read more</a>
    </div>
    <div class="card-footer">
      <a href="./BaseballCap-list1.html" data-i18n="baseball_discover_more">Discover more</a>
    </div>
  </div>
  <div class="card" id="card-2">
    <input type="checkbox" class="open-card" id="open-card-2" checked />
    <label class="open-card-label" for="open-card-2"></label>
    <input type="checkbox" class="card-toggle" id="card-toggle-2" />
    <label class="card-toggle-label" for="card-toggle-2"></label>
    <div class="card-image" style="background-image: url('./assets/img/cap/Baseball/2.avif');">
      <video class="card-video" muted loop></video>
    </div>
    <div class="card-header">
      <h2 data-i18n="method_2">Method 2</h2>
    </div>
    <div class="card-content left-content">
      <h3 data-i18n="baseball_left_title">Baseball Cap Style</h3>
      <p data-i18n="baseball_left_desc1">Classic baseball caps with adjustable straps, perfect for casual wear and sports activities.</p>
      <p data-i18n="baseball_left_desc2">Available in various colors and designs to match your personal style.</p>
      <a class="card-button" href="#" data-i18n="baseball_read_more">Read more</a>
    </div>
    <div class="card-content right-content">
      <h3 data-i18n="baseball_right_title">Premium Quality</h3>
      <p data-i18n="baseball_right_desc1">Made from high-quality materials that ensure durability and comfort during extended wear.</p>
      <p data-i18n="baseball_right_desc2">Breathable fabric and adjustable fit for all-day comfort in any weather.</p>
      <a class="card-button" href="#" data-i18n="baseball_read_more">Read more</a>
    </div>
    <div class="card-footer">
      <a href="./BaseballCap-list2.html" data-i18n="baseball_discover_more">Discover more</a>
    </div>
  </div>
  <div class="card" id="card-3">
    <input type="checkbox" class="open-card" id="open-card-3" checked />
    <label class="open-card-label" for="open-card-3"></label>
    <input type="checkbox" class="card-toggle" id="card-toggle-3" />
    <label class="card-toggle-label" for="card-toggle-3"></label>
    <div class="card-image" style="background-image: url('./assets/img/cap/Baseball/3.avif');">
      <video class="card-video" muted loop></video>
    </div>
    <div class="card-header">
      <h2 data-i18n="method_3">Method 3</h2>
    </div>
    <div class="card-content left-content">
      <h3 data-i18n="baseball_left_title">Baseball Cap Style</h3>
      <p data-i18n="baseball_left_desc1">Classic baseball caps with adjustable straps, perfect for casual wear and sports activities.</p>
      <p data-i18n="baseball_left_desc2">Available in various colors and designs to match your personal style.</p>
      <a class="card-button" href="#" data-i18n="baseball_read_more">Read more</a>
    </div>
    <div class="card-content right-content">
      <h3 data-i18n="baseball_right_title">Premium Quality</h3>
      <p data-i18n="baseball_right_desc1">Made from high-quality materials that ensure durability and comfort during extended wear.</p>
      <p data-i18n="baseball_right_desc2">Breathable fabric and adjustable fit for all-day comfort in any weather.</p>
      <a class="card-button" href="#" data-i18n="baseball_read_more">Read more</a>
    </div>
    <div class="card-footer">
      <a href="./BaseballCap-list3.html" data-i18n="baseball_discover_more">Discover more</a>
    </div>
  </div>
  <!-- 引入多语言支持脚本并初始化 -->
  <script src="assets/js/languages.js"></script>
  <!-- i18next库 -->
  <script src="https://cdn.jsdelivr.net/npm/i18next@21.6.10/i18next.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/i18next-browser-languagedetector@6.1.3/i18nextBrowserLanguageDetector.min.js"></script>
  <!-- 自定义i18n实现 -->
  <script src="assets/js/i18n.js"></script>
  <script>
    // 页面加载完成后初始化i18next
    document.addEventListener('DOMContentLoaded', function() {
      console.log('BaseballCap.html: DOM加载完成，准备初始化i18next');

      // 不再修改CSS规则，让"OPEN"文本正常显示
      console.log('保留CSS中的"OPEN"文本');
    });

    document.addEventListener('DOMContentLoaded', function() {

      // 初始化语言选择器
      document.getElementById('language-menu').addEventListener('click', function(e) {
        if (e.target.tagName === 'A') {
          const lang = e.target.dataset.lang;
          if (lang) {
            localStorage.setItem('selectedLanguage', lang);
            window.location.reload();
          }
        }
      });

      // 更新当前显示的语言标签
      const currentLang = localStorage.getItem('selectedLanguage') || 'en';
      const langDisplay = document.getElementById('current-language');
      if (langDisplay) {
        switch(currentLang) {
          case 'en': langDisplay.textContent = 'EN'; break;
          case 'zh': langDisplay.textContent = '中文'; break;
          case 'es': langDisplay.textContent = 'ES'; break;
          case 'ar': langDisplay.textContent = 'عربي'; break;
          case 'fr': langDisplay.textContent = 'FR'; break;
          case 'ru': langDisplay.textContent = 'RU'; break;
          case 'pt': langDisplay.textContent = 'PT'; break;
          case 'de': langDisplay.textContent = 'DE'; break;
          case 'ja': langDisplay.textContent = '日本語'; break;
          case 'hi': langDisplay.textContent = 'हिन्दी'; break;
          default: langDisplay.textContent = 'EN';
        }
      }

      // 设置卡片视频文件
      const cards = document.querySelectorAll('.card');

      cards.forEach((card, index) => {
        const cardImage = card.querySelector('.card-image');
        const video = card.querySelector('.card-video');

        if (cardImage && video) {
          // 根据卡片ID设置对应的视频路径
          const cardId = card.id;
          const cardNumber = cardId.split('-')[1]; // 获取卡片编号

          // 设置视频源为对应编号的MP4文件，注意这里路径改为Baseball
          const videoPath = `./assets/img/cap/Baseball/${cardNumber}.mp4`;
          video.src = videoPath;

          // 添加错误处理，当视频无法加载时显示提示
          console.log(`为卡片 ${cardId} 设置视频路径: ${videoPath}`);

          // 添加错误处理，当视频无法加载时显示提示
          video.addEventListener('error', function() {
            console.error(`视频文件未找到: ${video.src}，日后我们再补充`);
            // 可以在这里添加视觉提示，例如显示一个错误图标或消息
            const errorMsg = document.createElement('div');
            errorMsg.className = 'video-error-message';
            errorMsg.textContent = '视频文件未找到，日后我们再补充';
            cardImage.appendChild(errorMsg);
          });

          // 为整个卡片添加鼠标事件，确保所有区域都能激活视频
          function playVideo() {
            if (video.error) {
              // 如果视频加载失败，显示错误消息
              const errorMsg = cardImage.querySelector('.video-error-message');
              if (errorMsg) errorMsg.style.display = 'block';
            } else {
              // 否则正常播放视频
              video.style.opacity = '1';
              video.play().catch(e => {
                console.error('视频播放失败:', e);
              });
            }
          }

          function pauseVideo() {
            // 隐藏错误消息（如果有）
            const errorMsg = cardImage.querySelector('.video-error-message');
            if (errorMsg) errorMsg.style.display = 'none';

            // 隐藏并暂停视频
            video.style.opacity = '0';
            video.pause();
            video.currentTime = 0;
          }

          // 为卡片图片区域添加鼠标事件
          cardImage.addEventListener('mouseenter', playVideo);
          cardImage.addEventListener('mouseleave', pauseVideo);

          // 为整个卡片添加鼠标事件作为备用
          card.addEventListener('mouseenter', function(e) {
            // 只有当鼠标在图片区域时才播放视频
            const rect = cardImage.getBoundingClientRect();
            const mouseX = e.clientX;
            const mouseY = e.clientY;

            if (mouseX >= rect.left && mouseX <= rect.right &&
                mouseY >= rect.top && mouseY <= rect.bottom) {
              playVideo();
            }
          });

          card.addEventListener('mouseleave', pauseVideo);
        }
      });
    });
  </script>
</body>
</html>
