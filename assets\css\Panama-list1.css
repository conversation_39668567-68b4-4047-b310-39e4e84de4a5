:root {
	/* 调色板 */
	/* 深海藻：#0B1714 */
	--deep-seaweed--l: 19.21%; /* 亮度 */
	--deep-seaweed--c: 0.018; /* 彩度 */
	--deep-seaweed--h: 176.73; /* 色相 */
	--deep-seaweed: var(--deep-seaweed--l) var(--deep-seaweed--c) var(--deep-seaweed--h); /* 定义颜色值 */
  
	/* 更深的海藻：#172526 */
	--darker-seaweed--l: 25.22%; /* 亮度 */
	--darker-seaweed--c: 0.019; /* 彩度 */
	--darker-seaweed--h: 201.61; /* 色相 */
	--darker-seaweed: var(--darker-seaweed--l) var(--darker-seaweed--c) var(--darker-seaweed--h); /* 定义颜色值 */
  
	/* 燕麦色：#D0BEAD */
	--oatmeal--l: 81.19%; /* 亮度 */
	--oatmeal--c: 0.031; /* 彩度 */
	--oatmeal--h: 65.75; /* 色相 */
	--oatmeal: var(--oatmeal--l) var(--oatmeal--c) var(--oatmeal--h); /* 定义颜色值 */
  
	--primary--darkest: var(--deep-seaweed); /* 主色-最深 */
	--primary--darker: var(--darker-seaweed); /* 主色-较深 */
	--secondary: var(--oatmeal); /* 辅助色 */
  
	/* 字体设置 */
	--mono: "IBM Plex Mono", monospace; /* 等宽字体 */
	--sans: "IBM Plex Sans", sans-serif; /* 无衬线字体 */
  
	/* 内容设置 */
	--container--block-padding: 3.25rem; /* 块级容器内边距 */
	--container--inline-padding: min(360px, 4dvw); /* 内联容器内边距，最小360px，最大4dvw */
  
	/* 网格设置 */
	/* 来自CSS-Tricks的自适应填充CSS网格 + 灵活的最小宽度 */
	--grid-column-count: 4; /* 网格列数 */
  
	--grid-gap: 16px; /* 网格间隙 - 增加默认间隙 */
	--grid-gap-count: calc(var(--grid-column-count) - 1); /* 间隙数量计算 */
	--total-gap-width: calc(var(--grid-gap-count) * var(--grid-gap)); /* 总间隙宽度 */
  
	--grid-item--min-width--constraint: 280px; /* 网格项最小宽度限制 */
	--grid-item--max-width--constraint: 550px; /* 网格项最大宽度限制 */
	--grid-item--min-width: min(100%, var(--grid-item--min-width--constraint)); /* 网格项实际最小宽度 */
	--grid-item--max-width: calc((100% - var(--total-gap-width)) / var(--grid-column-count)); /* 网格项实际最大宽度 */
  
	--grid-item--bg: oklch(var(--primary--darker)); /* 网格项背景色 */
	--grid-item--color: oklch(var(--secondary)); /* 网格项文字颜色 */

	/* 响应式预览图高度设置 */
	--preview--h-desktop: 425px; /* 桌面端预览图高度 */
	--preview--h-laptop: 380px; /* 笔记本预览图高度 */
	--preview--h-tablet: 320px; /* 平板预览图高度 */
	--preview--h-mobile: 250px; /* 手机预览图高度 */
	--preview--h-small: 200px; /* 小型手机预览图高度 */
  }
  
  /* 用户代理覆盖 */
  *, *:before, *:after {
	box-sizing: border-box; /* 边框盒模型 */
	line-height: 1cap; /* 行高 */
  }
  
  /* 演示样式 */
  html {
	height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  color: #f0f0f0;
  background: #101522; /* 基础颜色 */
  background-blend-mode: hard-light; /* 混合模式 */
  background-image: radial-gradient(circle at 20% 20%, #ffcc7066 10%, #ffcc7000 50%), 
                    radial-gradient(circle at 80% 80%, #0033ff66 10%, #0033ff00 50%),
                    radial-gradient(ellipse at 35% 70%, #00ff4866 10%, #00ff4800 50%), 
                    radial-gradient(ellipse at 70% 35%, #ff005d66 10%, #ff005d00 60%);
  background-size: 250% 250%;
  animation: background-animation 30s infinite;
  }
  
  a:any-link {
	color: inherit; /* 继承颜色 */
  }
  
  a.button,
  a.tag {
	text-decoration: none; /* 无文本装饰 */
	transition-property: background, color; /* 过渡属性 */
  }
  
  .tags--overflow-container {	
	padding-inline: calc(var(--content-padding) + 4px); /* 内联填充 */
	position: relative; /* 相对定位 */
  
	/* 内联渐变 */
	&:after {
	  background: linear-gradient(
		90deg, 
		var(--grid-item--bg) var(--content-padding), 
		transparent calc(var(--content-padding) + 10px),
		transparent calc(100% - calc(var(--content-padding) + 10px)), 
		var(--grid-item--bg) calc(100% - var(--content-padding)))
	  ;
	  pointer-events: none; /* 无指针事件 */
  
	  content: "";
	  inset: 0;
	  position: absolute;
	  z-index: 6;
	}
  }
  
  .tags--container {
	container: tags-container / inline-size; /* 容器查询 */
	display: flex; /* 弹性盒子 */
	flex-wrap: wrap; /* 包裹 */
	gap: 4px; /* 间隙 */
	list-style: none; /* 无列表样式 */
	margin: 0; /* 外边距 */
	padding: 0; /* 内边距 */
	position: relative; /* 相对定位 */
  
	.tags--overflow-container & {
	  transform: translateX(0); /* X轴平移 */
	  will-change: transform; /* 改变属性优化 */
	}
  }
  
  a.tag,
  .tag {
	border: .05rem solid color-mix(in oklab, currentColor, transparent 80%); /* 边框颜色混合 */
	border-radius: 100px; /* 边框圆角 */
  
	display: flex; /* 弹性盒子 */
	align-items: center; /* 垂直居中 */
	font-size: 13px; /* 字体大小 */
	font-weight: 500; /* 字体粗细 */
	user-select: none; /* 禁止用户选择 */
  
	padding-inline: 14px; /* 内联填充 */
	padding-bottom: 1px; /* 底部填充 */
  
	transition-property: background, border, transform; /* 过渡属性 */
	transition-duration: .15s, .15s, 6s; /* 过渡时长 */
  
	height: 32px; /* 高度 */
  
	text-overlow: ellipsis; /* 文本溢出 */
	overflow: hidden; /* 隐藏溢出 */
	white-space: nowrap; /* 不换行 */
  
	&:not(.post-type):hover {
	  border-color: transparent; /* 边框颜色 */
	  background: color-mix(in oklab, currentColor, transparent 80%); /* 背景颜色混合 */
	}
	
	@media screen and (min-width: 600px) {
	  flex-wrap: nowrap; /* 不换行 */
	  overflow: hidden; /* 隐藏溢出 */
	}
  }
  
  .grid--container {
	display: grid; /* 网格显示 */
	grid-template-columns: repeat(var(--grid-column-count), minmax(var(--grid-item--min-width), 1fr)); /* 网格模板列 - 使用列数变量 */
	grid-auto-rows: minmax(450px, auto); /* 自动行高 */
	grid-auto-flow: dense; /* 密集流 */
	gap: var(--grid-gap); /* 间隙 */
	margin: 0 auto; /* 外边距自动 */
	max-width: 1920px; /* 最大宽度 */
	padding: 1.5rem; /* 增加内边距 */
  }
  
  /* 增强响应式布局的媒体查询 */
  @media screen and (max-width: 1440px) {
    .grid--container {
      padding: 1.25rem; /* 大屏幕上减小内边距 */
      max-width: 1400px; /* 限制最大宽度 */
    }
    
    .grid--item {
      --preview--h: var(--preview--h-laptop); /* 使用笔记本预览图高度 */
    }
  }
  
  @media screen and (max-width: 1200px) {
    :root {
      --grid-column-count: 3; /* 减少网格列数 */
    }
    
    .grid--container {
      grid-template-columns: repeat(var(--grid-column-count), minmax(300px, 1fr)); /* 调整网格项最小宽度并使用列数变量 */
    }
  }
  
  @media screen and (max-width: 992px) {
    .grid--container {
      padding: 1rem; /* 中等屏幕上减小内边距 */
      gap: 12px; /* 减小间隙 */
    }
    
    .grid--item {
      --preview--h: var(--preview--h-tablet); /* 使用平板预览图高度 */
    }
    
    .title--container a.title--text {
      font-size: 20px; /* 减小标题字体大小 */
    }
  }
  
  @media screen and (max-width: 768px) {
    :root {
      --grid-column-count: 2; /* 进一步减少网格列数 */
    }
    
    .grid--container {
      grid-template-columns: repeat(var(--grid-column-count), minmax(250px, 1fr)); /* 在小屏幕上减小最小宽度并使用列数变量 */
      gap: 10px; /* 减小间隙 */
      padding: 0.75rem; /* 减小内边距 */
    }
    
    .grid--item {
      --preview--h: var(--preview--h-mobile); /* 使用手机预览图高度 */
    }
    
    .tags--container {
      font-size: 11px; /* 减小标签字体大小 */
    }
    
    a.tag, .tag {
      padding-inline: 10px; /* 减小标签内边距 */
      height: 28px; /* 减小标签高度 */
    }
    
    .content--container {
      padding: 12px; /* 减小内容容器内边距 */
    }
    
    .title--container a.title--text {
      font-size: 18px; /* 进一步减小标题字体大小 */
      -webkit-line-clamp: 2; /* 限制标题行数 */
    }
  }
  
  @media screen and (max-width: 576px) {
    .grid--container {
      grid-template-columns: repeat(var(--grid-column-count), minmax(220px, 1fr)); /* 调整网格项最小宽度并使用列数变量 */
      gap: 8px; /* 进一步减小间隙 */
    }
  }
  
  @media screen and (max-width: 480px) {
    :root {
      --grid-column-count: 1; /* 单列布局 */
    }
    
    .grid--container {
      grid-template-columns: repeat(var(--grid-column-count), 1fr); /* 在极小屏幕上使用列数变量 */
      padding: 0.5rem; /* 最小内边距 */
    }
    
    .grid--item {
      --preview--h: var(--preview--h-small); /* 使用小型手机预览图高度 */
    }
    
    .hover--options .series, 
    .hover--options .latest {
      padding-inline: 12px 8px; /* 减小按钮内边距 */
    }
  }


/* GRID CELL */
.grid--cell {	
	position: relative; 
	
	&:nth-of-type(1) .preview-image { background-image: url("../img/cap/panama/list1/1.jpg"); }
	&:nth-of-type(2) .preview-image { background-image: url("../img/cap/panama/list1/2.jpg"); }
	&:nth-of-type(3) .preview-image { background-image: url("../img/cap/panama/list1/3.jpg"); }
	&:nth-of-type(4) .preview-image { background-image: url("../img/cap/panama/list1/4.jpg"); }
	&:nth-of-type(5) .preview-image { background-image: url("../img/cap/panama/list1/5.jpg"); }
	&:nth-of-type(6) .preview-image { background-image: url("../img/cap/panama/list1/6.jpg"); }
	&:nth-of-type(7) .preview-image { background-image: url("../img/cap/panama/list1/7.png"); }
	&:nth-of-type(8) .preview-image { background-image: url("../img/cap/panama/list1/8.png"); }
	&:nth-of-type(9) .preview-image { background-image: url("../img/cap/panama/list1/9.png"); }
	&:nth-of-type(10) .preview-image { background-image: url("../img/cap/panama/list1/10.png"); }
	&:nth-of-type(11) .preview-image { background-image: url("../img/cap/panama/list1/11.png"); }
	&:nth-of-type(12) .preview-image { background-image: url("../img/cap/panama/list1/12.jpeg"); }
}

/* GRID CELL */
.grid--cell {	
	position: relative; /* 相对定位 */
  
	&:nth-of-type(1) .preview-image { background-image: url("../img/cap/panama/list1/1.jpg"); } /* 第一个网格项的预览图 */
	&:nth-of-type(2) .preview-image { background-image: url("../img/cap/panama/list1/2.jpg"); } /* 第二个网格项的预览图 */
	&:nth-of-type(3) .preview-image { background-image: url("../img/cap/panama/list1/3.jpg"); } /* 第三个网格项的预览图 */
	&:nth-of-type(4) .preview-image { background-image: url("../img/cap/panama/list1/4.jpg"); } /* 第四个网格项的预览图 */
	&:nth-of-type(5) .preview-image { background-image: url("../img/cap/panama/list1/5.jpg"); } /* 第五个网格项的预览图 */
	&:nth-of-type(6) .preview-image { background-image: url("../img/cap/panama/list1/6.jpg"); } /* 第六个网格项的预览图 */
	&:nth-of-type(7) .preview-image { background-image: url("../img/cap/panama/list1/7.png"); } /* 第七个网格项的预览图 */
	&:nth-of-type(8) .preview-image { background-image: url("../img/cap/panama/list1/8.png"); } /* 第八个网格项的预览图 */
	&:nth-of-type(9) .preview-image { background-image: url("../img/cap/panama/list1/9.png"); } /* 第九个网格项的预览图 */
	&:nth-of-type(10) .preview-image { background-image: url("../img/cap/panama/list1/10.png"); } /* 第十个网格项的预览图 */
	&:nth-of-type(11) .preview-image { background-image: url("../img/cap/panama/list1/11.png"); } /* 第十一个网格项的预览图 */
	&:nth-of-type(12) .preview-image { background-image: url("../img/cap/panama/list1/12.jpeg"); } /* 第十二个网格项的预览图 */
  }
  
  /* GRID ITEM */
  .grid--item {	
	--preview--h: var(--preview--h-desktop, 425px); /* 预览图高度，默认使用桌面端高度 */
  
	--content-gap: 16px; /* 内容间隙 */
	--content-padding: 12px; /* 内容内边距 */
  
	--options--h: 42px; /* 选项高度 */
	--hover--h: calc(var(--content-gap) + var(--options--h)); /* 悬停高度 */
	--hover-offset: calc((var(--hover--h) / 2) * -1); /* 悬停偏移 */
  
	--shadow-blur: 36px; /* 阴影模糊度 */
	--shadow-transparency: 60%; /* 阴影透明度 */
  
	border-radius: 18px; /* 边框圆角 */
	color: var(--grid-item--color); /* 文字颜色 */
	container: grid-item / inline-size; /* 容器 */
  
	display: grid; /* 网格显示 */
	grid-template-rows: var(--preview--h) 1fr; /* 网格模板行 */
	align-content: start; /* 内容对齐开始 */
	filter: drop-shadow(0 0 var(--shadow-blur) color-mix(in oklab, oklch(var(--primary--darkest)), transparent var(--shadow-transparency))); /* 阴影滤镜 */
  
	position: relative; /* 相对定位 */
	transition-property: transform, filter; /* 过渡属性 */
	transition-duration: .3s; /* 过渡时长 */
  
	will-change: height, transform; /* 改变属性优化 */
	height: 100%; /* 高度 */
	width: 100%; /* 宽度 */
  
	/* 背景 */
	&:before {
	  background: var(--grid-item--bg); /* 背景颜色 */
	  clip-path: inset(0% 0% round 18px); /* 裁剪路径 */		
	  will-change: background; /* 改变属性优化 */
  
	  content: "";
	  inset: 0;
	  position: absolute;
	  z-index: -1;
	}
  
	.preview-image--container,
	.preview-image {
	  height: 100%; /* 高度 */
	  width: 100%; /* 宽度 */
	}
  
	.preview--container {
	  background: oklch(var(--secondary)); /* 背景颜色 */
	  display: flex; /* 弹性盒子 */
	  align-items: flex-end; /* 项目底部对齐 */
	  justify-content: flex-end; /* 项目右侧对齐 */
  
	  position: relative; /* 相对定位 */
	  height: var(--preview--h); /* 高度 */
	  width: 100%; /* 宽度 */
	}
		  
	.preview-image {
	  --bg-scale: 1.15, 1.15; /* 背景缩放 */
	  --bg-position: 50% 50%; /* 背景位置 */
  
	  background-size: cover; /* 背景覆盖 */
	  transform: scale(var(--bg-scale)) translateZ(1px); /* 缩放和3D变换 */
	  background-position: var(--bg-position); /* 背景位置 */
	  background-repeat: no-repeat; /* 背景不重复 */
	  filter: grayscale(100%); /* 灰度滤镜 */
	  mix-blend-mode: multiply; /* 混合模式 */
  
	  will-change: background, transform; /* 改变属性优化 */
  
	  transition-property: background, filter, mix-blend-mode, opacity, transform; /* 过渡属性 */
	  transition-duration: 4.5s, .15s, .15s, .3s, 3s; /* 过渡时长 */
	}
  
	.meta--container {
	  display: flex; /* 弹性盒子 */
	  flex-wrap: wrap; /* 包裹 */
	  font-family: var(--mono); /* 字体 */
	  font-size: 11px; /* 字体大小 */
	  font-weight: 500; /* 字体粗细 */
	  height: 30px; /* 高度 */
	  position: absolute; /* 绝对定位 */
	  z-index: 6; /* 堆叠顺序 */
  
	  .issue,
	  .page {				
		align-items: center; /* 项目居中对齐 */
		display: flex; /* 弹性盒子 */
  
		height: 100%; /* 高度 */
		padding-inline: 12px; /* 内联填充 */
		text-decoration: none; /* 无文本装饰 */
  
		&:hover { text-decoration: underline;	} /* 悬停文本装饰 */
	  }
  
	  /* .issue { 
		background: oklch(var(--primary--darkest));
		border-radius: 4px 0 0 0;
		color: oklch(var(--secondary));
	  }
	  
	  .page {
		background: var(--grid-item--bg);
	  } */
	}
  
	.content--container {		
	  clip-path: inset(0% 0% round 0 0 18px 18px); /* 裁剪路径 */ /* <3 */
	  display: grid; /* 网格显示 */
	  align-self: stretch; /* 自身拉伸 */
	  gap: var(--content-gap); /* 间隙 */
	  padding-block: var(--content-padding) calc(var(--content-padding) + 2px); /* 块级填充 */
	  height: 100%; /* 高度 */
	  
	  .title--container,
	  .tags--container,
	  .hover--options {
		width: 100%; /* 宽度 */
	  }
	  
	  .title--container {			
		align-self: stretch; /* 自身拉伸 */
		height: auto; /* 高度自动 */
		padding-inline: calc(var(--content-padding) + 8px); /* 内联填充 */
  
		a.title--text {
		  font-size: 22px; /* 字体大小 */
		  font-weight: 500; /* 字体粗细 */
		  line-height: 1.2; /* 行高 */
		  margin: 0; /* 外边距 */
		  padding-bottom: 1px; /* 底部填充 */
		  text-decoration-color: color-mix(in oklab, currentColor, transparent 86%); /* 文本装饰颜色 */
		  text-decoration-skip-ink: none; /* 文本装饰跳过 */
		  text-decoration-thickness: 3px; /* 文本装饰厚度 */
  
		  display: -webkit-box; /* WebKit盒子 */
		  -webkit-line-clamp: 10; /* WebKit行数限制 */
		  -webkit-box-orient: vertical; /* WebKit盒子方向 */
		  overflow: hidden; /* 隐藏溢出 */
		  text-overflow: ellipsis;
				
		  &:hover {
			text-decoration-color: color-mix(in oklab, currentColor, transparent 72%); /* 悬停文本装饰颜色 */
		  }
		}
	  }
	  
	  /* 标题省略号配置 */
	  /* 注意：保持这样，不要移动到@container */
	  @media screen and (min-width: 320px) { 
		.title--container { 
		  min-height: 2.4rlh; /* 最小高度 */
	  
		  a.title--text { -webkit-line-clamp: 2; } /* WebKit行数限制 */
		}
	  }
	  @media screen and (min-width: 600px) { 
		.title--container { 
		  min-height: unset; /* 最小高度取消 */
		  a.title--text { -webkit-line-clamp: 1; } /* WebKit行数限制 */
		}
	  }
	  /* */
	  
	  .tags--overflow-container {
		display: flex; /* 弹性盒子 */
		align-items: flex-end; /* 项目底部对齐 */
	  }
	}
	
	.hover--options {
		display: none; 
		flex-wrap: wrap; 
		gap: 6px;
		margin-top: -4px;
		padding-inline: calc(var(--content-padding) + 4px);
		
		.button {
			border-radius: 100px; /* 边框圆角 */
			display: flex; /* 弹性盒子 */
			height: var(--options--h); /* 高度 */
			align-items: center; /* 项目居中对齐 */
			font-weight: 600; /* 字体粗细 */
			transition-duration: .15s; /* 过渡时长 */
		  }
		  
		  .follow {
			border: 4px solid color-mix(in oklab, currentColor, transparent 80%); /* 边框颜色混合 */
			border-radius: 100px; /* 边框圆角 */
			font-size: 18px; /* 字体大小 */
			display: flex; /* 弹性盒子 */
			align-items: center; /* 项目居中对齐 */
			justify-content: center; /* 项目居中 */
			line-height: 0; /* 行高 */
			width: var(--options--h); /* 宽度 */
		  
			transition-property: background, border; /* 过渡属性 */
		  
			&:hover {
			  border-color: transparent; /* 悬停边框颜色 */
			  background: color-mix(in oklab, currentColor, transparent 80%); /* 悬停背景颜色混合 */
			}
		  }
		  
		  .series,
		  .latest {
			display: flex; /* 弹性盒子 */
			justify-content: center; /* 项目居中 */
			gap: 12px; /* 间隙 */
			flex: 1; /* 弹性 */
			padding-inline: 8px 14px; /* 内联填充 */
			position: relative; /* 相对定位 */
		  
			.icon-title { 
			  display: flex; /* 弹性盒子 */
			  align-items: center; /* 项目居中对齐 */
			  height: 100%; /* 高度 */
			  gap: 8px; /* 间隙 */
			}
		  
			[data-icon="books"],
			[data-icon="image"] { 
			  opacity: 75%; /* 不透明度 */
			}
		  
			.new-tab {
			  display: none; /* 默认不显示 */
			  opacity: 45%; /* 不透明度 */
			  rotate: -36deg; /* 旋转 */
		  
			  will-change: display; /* 改变属性优化 */
			}
		  }
		  
		  .series {
			background: color-mix(in oklab, currentColor, transparent 90%); /* 背景颜色混合 */
		  
			&:hover {
			  background: color-mix(in oklab, currentColor, transparent 80%); /* 悬停背景颜色混合 */
			  color: oklch(var(--primary--darkest)); /* 悬停文字颜色 */
			}
		  }
		  
		  .latest {
			background: oklch(var(--primary--darker)); /* 背景颜色 */
			color: oklch(var(--secondary)); /* 文字颜色 */
		  
			&:hover {
			  background: oklch(var(--primary--darkest)); /* 悬停背景颜色 */
			}
		  }
	}
		
	&:not(.featured) {
		.preview--container {
			clip-path: inset(0% 0% round 18px 18px 0 0); /* <3 */
		}
	}
	
	&.featured {
		
		.post-type {
			left: -12px;
			top: 12px;
			position: absolute; /* 绝对定位 */
		  }
				  
		  .preview--container {
			clip-path: inset(0% 0% round 18px 18px 0 0); /* 裁剪路径 */
			justify-content: unset; /* 取消居中对齐 */
		  }
		  
		  .tags--container {
			margin-bottom: 12px; /* 底部外边距 */
			position: absolute; /* 绝对定位 */
			z-index: 6; /* 堆叠顺序 */
		  }
		  
		  .content--container { padding-bottom: calc(var(--content-padding) + 4px); } /* 底部填充 */
		  
		  .intro--container,
		  .description--container {
			line-height: 2.2ex; /* 行高 */
			margin: 0; /* 外边距 */
			padding-inline: calc(var(--content-padding) + 8px); /* 内联填充 */
		  }
		  
		  @media screen and (min-width: 652px) {
			grid-template-columns: 1fr 1fr; /* 网格模板列 */
		  
			.preview--container {
			  clip-path: inset(0% 0% round 0 18px 18px 0); /* 裁剪路径 */
			  height: 100%; /* 高度 */
			  order: 2; /* 顺序 */
			}
		  }
	}
	
	/* Grid Cell :hover/:focus */
	&:hover, 
	&:focus {
		--grid-item--bg: oklch(var(--secondary));
		--grid-item--color: oklch(var(--primary--darker));
		
		height: auto; /* 高度自动 */
		transform: translateY(var(--hover-offset)); /* Y轴平移 */
		position: absolute; /* 绝对定位 */
		z-index: 5; /* 堆叠顺序 */
		
		&:before {
		  --shadow-blur: 90px; /* 阴影模糊度 */
		  --shadow-transparency: 0%; /* 阴影透明度 */
		}
					
		.hover--options { display: flex; } /* 显示悬停选项 */		
		.preview--container { background: oklch(var(--primary--darkest)); } /* 悬停背景颜色 */
		
		.preview-image {
		  --bg-scale: 1, 1; /* 背景缩放 */
		  --bg-position: 50% 100%; /* 背景位置 */
		
		  filter: revert; /* 撤销滤镜 */
		  mix-blend-mode: revert; /* 撤销混合模式 */
		  opacity: revert; /* 撤销不透明度 */
		}
	}
}

/* `grid-item` Container Queries */
@media screen and (min-width: 600px),
@container grid-item (min-width: 300px) {	
	.tags--container { flex-wrap: nowrap;	}
}

@container grid-item (min-width: 360px) {
	.grid--item {
		
		.hover--options {
			.series,
			.latest {				
				justify-content: space-between;
				padding-inline: 18px 12px;
				
				.new-tab { display: block; }
			}
		}
	}
}
/*  */

/* MEDIA QUERIES */
:root {
	@media screen and (min-width: 800px) { --grid-item--min-width--constraint: 500px; }
}

@media screen and (min-width: 600px) {
	.grid--item {
		&:hover {
			.tags--container {
				animation: marquee 10s linear alternate infinite;
				animation-delay: .6s;
				animation-timing-function: ease-out;
				
				&:hover {
					animation-play-state: paused;
				}
			}			
		}
	}
}
/*  */

/* ANIMATIONS */
@keyframes marquee {
  0% { transform: translateX(0); }
  100% { transform: translateX(-48%); }
}

