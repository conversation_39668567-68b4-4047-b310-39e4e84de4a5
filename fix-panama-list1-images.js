// 修复 PanamaCap-list1.html 中所有产品图片的脚本
// 为所有产品图片添加直接的背景图片设置

const fs = require('fs');

// 读取文件内容
let content = fs.readFileSync('PanamaCap-list1.html', 'utf8');

// 产品图片的替换规则
const imageReplacements = [
    {
        from: '<div class="product-image" data-image-base="assets/img/cap/panama/list1/1"></div>',
        to: '<div class="product-image" data-image-base="assets/img/cap/panama/list1/1" style="background-image: url(\'assets/img/cap/panama/list1/1.jpg\');"></div>'
    },
    {
        from: '<div class="product-image" data-image-base="assets/img/cap/panama/list1/2"></div>',
        to: '<div class="product-image" data-image-base="assets/img/cap/panama/list1/2" style="background-image: url(\'assets/img/cap/panama/list1/2.jpg\');"></div>'
    },
    {
        from: '<div class="product-image" data-image-base="assets/img/cap/panama/list1/3"></div>',
        to: '<div class="product-image" data-image-base="assets/img/cap/panama/list1/3" style="background-image: url(\'assets/img/cap/panama/list1/3.jpg\');"></div>'
    },
    {
        from: '<div class="product-image" data-image-base="assets/img/cap/panama/list1/4"></div>',
        to: '<div class="product-image" data-image-base="assets/img/cap/panama/list1/4" style="background-image: url(\'assets/img/cap/panama/list1/4.jpg\');"></div>'
    },
    {
        from: '<div class="product-image" data-image-base="assets/img/cap/panama/list1/5"></div>',
        to: '<div class="product-image" data-image-base="assets/img/cap/panama/list1/5" style="background-image: url(\'assets/img/cap/panama/list1/5.jpg\');"></div>'
    },
    {
        from: '<div class="product-image" data-image-base="assets/img/cap/panama/list1/6"></div>',
        to: '<div class="product-image" data-image-base="assets/img/cap/panama/list1/6" style="background-image: url(\'assets/img/cap/panama/list1/6.jpg\');"></div>'
    },
    {
        from: '<div class="product-image" data-image-base="assets/img/cap/panama/list1/7"></div>',
        to: '<div class="product-image" data-image-base="assets/img/cap/panama/list1/7" style="background-image: url(\'assets/img/cap/panama/list1/7.png\');"></div>'
    },
    {
        from: '<div class="product-image" data-image-base="assets/img/cap/panama/list1/8"></div>',
        to: '<div class="product-image" data-image-base="assets/img/cap/panama/list1/8" style="background-image: url(\'assets/img/cap/panama/list1/8.png\');"></div>'
    },
    {
        from: '<div class="product-image" data-image-base="assets/img/cap/panama/list1/9"></div>',
        to: '<div class="product-image" data-image-base="assets/img/cap/panama/list1/9" style="background-image: url(\'assets/img/cap/panama/list1/9.png\');"></div>'
    },
    {
        from: '<div class="product-image" data-image-base="assets/img/cap/panama/list1/10"></div>',
        to: '<div class="product-image" data-image-base="assets/img/cap/panama/list1/10" style="background-image: url(\'assets/img/cap/panama/list1/10.png\');"></div>'
    },
    {
        from: '<div class="product-image" data-image-base="assets/img/cap/panama/list1/11"></div>',
        to: '<div class="product-image" data-image-base="assets/img/cap/panama/list1/11" style="background-image: url(\'assets/img/cap/panama/list1/11.png\');"></div>'
    },
    {
        from: '<div class="product-image" data-image-base="assets/img/cap/panama/list1/12"></div>',
        to: '<div class="product-image" data-image-base="assets/img/cap/panama/list1/12" style="background-image: url(\'assets/img/cap/panama/list1/12.jpeg\');"></div>'
    }
];

// 应用所有替换
let replacedCount = 0;
imageReplacements.forEach(replacement => {
    if (content.includes(replacement.from)) {
        content = content.replace(replacement.from, replacement.to);
        replacedCount++;
        console.log(`✅ 已替换: ${replacement.from.substring(0, 50)}...`);
    } else {
        console.log(`❌ 未找到: ${replacement.from.substring(0, 50)}...`);
    }
});

// 写回文件
fs.writeFileSync('PanamaCap-list1.html', content);

console.log(`\n🎉 修复完成！共替换了 ${replacedCount} 个产品图片`);
console.log('现在 PanamaCap-list1.html 的产品图片应该能正常显示了！');
