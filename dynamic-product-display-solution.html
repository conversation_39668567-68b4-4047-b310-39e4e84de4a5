<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>动态产品显示解决方案</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe, #00f2fe);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .content {
            padding: 30px;
        }
        
        .status {
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border-left: 5px solid;
        }
        
        .status.success {
            background-color: #f0fff4;
            border-color: #38a169;
            color: #2f855a;
        }
        
        .status.warning {
            background-color: #fffbeb;
            border-color: #ed8936;
            color: #c05621;
        }
        
        .status.info {
            background-color: #ebf8ff;
            border-color: #3182ce;
            color: #2c5282;
        }
        
        .example-box {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            margin: 10px;
            transition: transform 0.3s ease;
            font-weight: 500;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 动态产品显示解决方案</h1>
            <p>根据实际存在的产品数量动态显示，而不是固定显示12个产品位置</p>
        </div>
        
        <div class="content">
            <div class="status info">
                <h2>📋 问题描述</h2>
                <p><strong>当前问题：</strong> PanamaCap-list3.html 固定显示12个产品位置，但实际只有5个产品图片</p>
                <p><strong>期望效果：</strong> 只显示实际存在的产品，有多少个产品就显示多少个</p>
                <p><strong>适用范围：</strong> 所有产品页面都应该支持动态产品数量</p>
            </div>

            <div class="status success">
                <h2>✅ 解决方案</h2>
                <div class="example-box">
                    <h4>方案1：动态产品检测和隐藏</h4>
                    <p><strong>原理：</strong> 页面加载时检测每个产品的图片是否存在，如果不存在则隐藏该产品卡片</p>
                    <p><strong>优点：</strong> 不需要修改HTML结构，只需要添加JavaScript逻辑</p>
                    <p><strong>实现：</strong> 使用图片加载检测，失败的产品自动隐藏</p>
                </div>
                
                <div class="example-box">
                    <h4>方案2：动态产品生成</h4>
                    <p><strong>原理：</strong> 根据实际存在的图片数量动态生成产品卡片</p>
                    <p><strong>优点：</strong> 完全动态，支持任意数量的产品</p>
                    <p><strong>实现：</strong> 检测图片目录，根据存在的图片生成对应的产品卡片</p>
                </div>
            </div>

            <div class="status warning">
                <h2>🔧 推荐实现：方案1 - 动态隐藏</h2>
                <div class="example-box">
                    <h4>实现步骤：</h4>
                    <ol>
                        <li><strong>检测产品图片</strong> - 使用 Image 对象检测每个产品图片是否存在</li>
                        <li><strong>隐藏无效产品</strong> - 图片加载失败的产品卡片自动隐藏</li>
                        <li><strong>调整布局</strong> - 确保隐藏产品后布局仍然美观</li>
                        <li><strong>更新计数</strong> - 更新实际显示的产品数量</li>
                    </ol>
                </div>
                
                <div class="example-box">
                    <h4>核心代码：</h4>
                    <div class="code-block">
// 动态检测并隐藏不存在的产品
function hideNonExistentProducts() {
    const productCards = document.querySelectorAll('.product-card');
    let visibleCount = 0;
    
    productCards.forEach((card, index) => {
        const productImage = card.querySelector('.product-image');
        const imageUrl = productImage.style.backgroundImage.match(/url\(['"]?([^'")]+)['"]?\)/);
        
        if (imageUrl && imageUrl[1]) {
            const img = new Image();
            img.onload = function() {
                // 图片存在，保持显示
                card.style.display = 'block';
                visibleCount++;
                console.log(`产品 ${index + 1} 图片存在，保持显示`);
            };
            img.onerror = function() {
                // 图片不存在，隐藏产品卡片
                card.style.display = 'none';
                console.log(`产品 ${index + 1} 图片不存在，已隐藏`);
            };
            img.src = imageUrl[1];
        }
    });
    
    // 延迟更新计数，确保所有检测完成
    setTimeout(() => {
        console.log(`实际显示的产品数量: ${visibleCount}`);
    }, 1000);
}

// 页面加载完成后执行检测
document.addEventListener('DOMContentLoaded', function() {
    hideNonExistentProducts();
});
                    </div>
                </div>
            </div>

            <div class="status info">
                <h2>🎯 具体修改计划</h2>
                <div class="example-box">
                    <h4>需要修改的文件：</h4>
                    <ul>
                        <li><strong>PanamaCap-list3.html</strong> - 添加动态产品检测逻辑</li>
                        <li><strong>其他产品页面</strong> - 可选：为所有页面添加相同逻辑</li>
                    </ul>
                </div>
                
                <div class="example-box">
                    <h4>修改内容：</h4>
                    <ol>
                        <li>在页面加载时添加产品图片检测函数</li>
                        <li>对于图片加载失败的产品，自动隐藏整个产品卡片</li>
                        <li>确保隐藏产品后网格布局仍然正常</li>
                        <li>在控制台输出实际显示的产品数量</li>
                    </ol>
                </div>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <h3>🚀 开始实施修改</h3>
                <a href="#" class="btn" onclick="alert('即将为 PanamaCap-list3.html 添加动态产品检测功能')">
                    修改 PanamaCap-list3.html
                </a>
                <p style="color: #666; font-style: italic; margin-top: 20px;">
                    修改后，页面将只显示实际存在的产品，自动隐藏不存在的产品卡片
                </p>
            </div>

            <div class="status success">
                <h2>🎊 预期效果</h2>
                <div class="example-box">
                    <h4>修改后的效果：</h4>
                    <ul>
                        <li>✅ <strong>只显示存在的产品</strong> - 如果只有5个产品图片，就只显示5个产品卡片</li>
                        <li>✅ <strong>自动适应数量</strong> - 未来添加更多产品时，会自动显示新产品</li>
                        <li>✅ <strong>布局保持美观</strong> - 隐藏产品后，剩余产品仍然排列整齐</li>
                        <li>✅ <strong>无需手动维护</strong> - 添加或删除产品图片时，页面自动调整</li>
                        <li>✅ <strong>控制台反馈</strong> - 显示实际检测到的产品数量</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
