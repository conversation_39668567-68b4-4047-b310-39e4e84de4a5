<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>调试 PanamaCap-list2.html</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .debug-info { background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 4px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        .warning { background: #fff3e0; color: #ef6c00; }
    </style>
</head>
<body>
    <h1>PanamaCap-list2.html 调试信息</h1>
    
    <div id="debug-output"></div>
    
    <button onclick="testPage()">测试页面功能</button>
    <button onclick="openPage()">打开 PanamaCap-list2.html</button>
    
    <script>
        function log(message, type = 'info') {
            const output = document.getElementById('debug-output');
            const div = document.createElement('div');
            div.className = `debug-info ${type}`;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            output.appendChild(div);
        }
        
        function testPage() {
            log('开始测试 PanamaCap-list2.html...', 'warning');
            
            // 创建一个隐藏的iframe来加载页面
            const iframe = document.createElement('iframe');
            iframe.style.display = 'none';
            iframe.src = 'PanamaCap-list2.html';
            
            iframe.onload = function() {
                try {
                    const doc = iframe.contentDocument || iframe.contentWindow.document;
                    
                    // 检查模态框元素
                    const modal = doc.getElementById('productModal');
                    if (modal) {
                        log('✅ 模态框元素存在', 'success');
                    } else {
                        log('❌ 模态框元素不存在', 'error');
                    }
                    
                    // 检查产品卡片
                    const productCards = doc.querySelectorAll('.product-card');
                    log(`✅ 找到 ${productCards.length} 个产品卡片`, 'success');
                    
                    // 检查产品图片
                    const productImages = doc.querySelectorAll('.product-image');
                    log(`✅ 找到 ${productImages.length} 个产品图片`, 'success');
                    
                    // 检查JavaScript函数
                    const win = iframe.contentWindow;
                    if (typeof win.initializeProducts === 'function') {
                        log('✅ initializeProducts 函数存在', 'success');
                    } else {
                        log('❌ initializeProducts 函数不存在', 'error');
                    }
                    
                    // 检查事件监听器
                    setTimeout(() => {
                        const firstImage = doc.querySelector('.product-image');
                        if (firstImage) {
                            // 模拟点击
                            const event = new doc.defaultView.MouseEvent('click', {
                                bubbles: true,
                                cancelable: true,
                                view: doc.defaultView
                            });
                            
                            firstImage.dispatchEvent(event);
                            
                            setTimeout(() => {
                                const modal = doc.getElementById('productModal');
                                if (modal && modal.classList.contains('open')) {
                                    log('✅ 模态框成功打开！', 'success');
                                } else {
                                    log('❌ 模态框未打开', 'error');
                                    log('可能的问题：事件监听器未正确绑定', 'warning');
                                }
                            }, 100);
                        }
                    }, 1000);
                    
                } catch (error) {
                    log(`❌ 测试出错: ${error.message}`, 'error');
                }
            };
            
            iframe.onerror = function() {
                log('❌ 页面加载失败', 'error');
            };
            
            document.body.appendChild(iframe);
            
            // 5秒后移除iframe
            setTimeout(() => {
                document.body.removeChild(iframe);
                log('测试完成', 'warning');
            }, 5000);
        }
        
        function openPage() {
            window.open('PanamaCap-list2.html', '_blank');
            log('已在新窗口打开 PanamaCap-list2.html', 'success');
        }
        
        // 页面加载时的说明
        window.onload = function() {
            log('调试工具已准备就绪', 'success');
            log('问题分析：PanamaCap-list2.html 中可能存在以下问题：', 'warning');
            log('1. initializeProducts 函数在调用时尚未定义', 'warning');
            log('2. 事件监听器绑定时机不正确', 'warning');
            log('3. 模态框CSS样式问题', 'warning');
            log('4. JavaScript执行顺序问题', 'warning');
        };
    </script>
</body>
</html>
