// 确保这个选择器与HTML中的类匹配
const aboutSection = document.querySelector('.about-section');
const leftSplit = aboutSection.querySelector('.split.left');
const rightSplit = aboutSection.querySelector('.split.right');
const container = aboutSection.querySelector('.containersx');

leftSplit.addEventListener("mouseenter", () =>
  container.classList.add("hover-left")
);
leftSplit.addEventListener("mouseleave", () =>
  container.classList.remove("hover-left")
);
rightSplit.addEventListener("mouseenter", () =>
  container.classList.add("hover-right")
);
rightSplit.addEventListener("mouseleave", () =>
  container.classList.remove("hover-right")
);
