<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>PanamaCap-list1.html 图片诊断工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .image-test {
            display: inline-block;
            margin: 10px;
            padding: 15px;
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            text-align: center;
            min-width: 200px;
        }
        .image-preview {
            width: 150px;
            height: 150px;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            border-radius: 8px;
            margin: 10px auto;
            border: 2px solid rgba(255,255,255,0.3);
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            margin: 5px 0;
            font-weight: bold;
        }
        .success { background: rgba(76, 175, 80, 0.3); }
        .error { background: rgba(244, 67, 54, 0.3); }
        .warning { background: rgba(255, 193, 7, 0.3); }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            margin: 10px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 PanamaCap-list1.html 图片诊断工具</h1>
        
        <button class="btn" onclick="testAllImages()">开始检测所有图片</button>
        <button class="btn" onclick="generateFixScript()">生成修复脚本</button>
        
        <div id="results"></div>
        
        <div id="fix-script" style="display:none;">
            <h2>🔧 修复脚本</h2>
            <textarea id="script-content" style="width:100%; height:200px; background:rgba(0,0,0,0.3); color:white; border:none; padding:10px;"></textarea>
        </div>
    </div>

    <script>
        const imageList = [
            { id: 1, formats: ['jpg', 'jpeg', 'png', 'webp'] },
            { id: 2, formats: ['jpg', 'jpeg', 'png', 'webp'] },
            { id: 3, formats: ['jpg', 'jpeg', 'png', 'webp'] },
            { id: 4, formats: ['jpg', 'jpeg', 'png', 'webp'] },
            { id: 5, formats: ['jpg', 'jpeg', 'png', 'webp'] },
            { id: 6, formats: ['jpg', 'jpeg', 'png', 'webp'] },
            { id: 7, formats: ['png', 'jpg', 'jpeg', 'webp'] },
            { id: 8, formats: ['png', 'jpg', 'jpeg', 'webp'] },
            { id: 9, formats: ['png', 'jpg', 'jpeg', 'webp'] },
            { id: 10, formats: ['png', 'jpg', 'jpeg', 'webp'] },
            { id: 11, formats: ['png', 'jpg', 'jpeg', 'webp'] },
            { id: 12, formats: ['jpeg', 'jpg', 'png', 'webp'] }
        ];

        let testResults = [];

        async function testImage(id, format) {
            return new Promise((resolve) => {
                const img = new Image();
                const url = `assets/img/cap/panama/list1/${id}.${format}`;
                
                img.onload = function() {
                    resolve({ id, format, url, status: 'success', width: this.width, height: this.height });
                };
                
                img.onerror = function() {
                    resolve({ id, format, url, status: 'error' });
                };
                
                img.src = url;
            });
        }

        async function testAllImages() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<h2>🔍 检测结果</h2>';
            testResults = [];

            for (const item of imageList) {
                const testDiv = document.createElement('div');
                testDiv.className = 'image-test';
                testDiv.innerHTML = `
                    <h3>Product ${item.id}</h3>
                    <div class="image-preview" id="preview-${item.id}"></div>
                    <div id="status-${item.id}">检测中...</div>
                `;
                resultsDiv.appendChild(testDiv);

                let foundFormat = null;
                for (const format of item.formats) {
                    const result = await testImage(item.id, format);
                    if (result.status === 'success') {
                        foundFormat = format;
                        testResults.push(result);
                        break;
                    }
                }

                const statusDiv = document.getElementById(`status-${item.id}`);
                const previewDiv = document.getElementById(`preview-${item.id}`);
                
                if (foundFormat) {
                    statusDiv.innerHTML = `<div class="status success">✅ 找到: ${foundFormat}</div>`;
                    previewDiv.style.backgroundImage = `url('assets/img/cap/panama/list1/${item.id}.${foundFormat}')`;
                } else {
                    statusDiv.innerHTML = `<div class="status error">❌ 未找到任何格式</div>`;
                    previewDiv.style.backgroundColor = '#ff6b6b';
                    previewDiv.innerHTML = '<div style="line-height:150px;">图片缺失</div>';
                }
            }

            // 显示总结
            const summary = document.createElement('div');
            summary.innerHTML = `
                <h2>📊 检测总结</h2>
                <div class="status success">✅ 成功: ${testResults.length} 个图片</div>
                <div class="status error">❌ 失败: ${imageList.length - testResults.length} 个图片</div>
            `;
            resultsDiv.appendChild(summary);
        }

        function generateFixScript() {
            if (testResults.length === 0) {
                alert('请先运行图片检测！');
                return;
            }

            const scriptDiv = document.getElementById('fix-script');
            const scriptContent = document.getElementById('script-content');
            
            let script = `// 基于检测结果的 PanamaCap-list1.html 修复脚本
const fs = require('fs');

let content = fs.readFileSync('PanamaCap-list1.html', 'utf8');

const corrections = [
`;

            testResults.forEach(result => {
                script += `    {
        from: 'data-image-base="assets/img/cap/panama/list1/${result.id}" style="background-image: url(\\'assets/img/cap/panama/list1/${result.id}.jpg\\');"',
        to: 'data-image-base="assets/img/cap/panama/list1/${result.id}" style="background-image: url(\\'assets/img/cap/panama/list1/${result.id}.${result.format}\\');"'
    },
    {
        from: 'data-image-base="assets/img/cap/panama/list1/${result.id}" style="background-image: url(\\'assets/img/cap/panama/list1/${result.id}.png\\');"',
        to: 'data-image-base="assets/img/cap/panama/list1/${result.id}" style="background-image: url(\\'assets/img/cap/panama/list1/${result.id}.${result.format}\\');"'
    },
    {
        from: 'data-image-base="assets/img/cap/panama/list1/${result.id}" style="background-image: url(\\'assets/img/cap/panama/list1/${result.id}.jpeg\\');"',
        to: 'data-image-base="assets/img/cap/panama/list1/${result.id}" style="background-image: url(\\'assets/img/cap/panama/list1/${result.id}.${result.format}\\');"'
    },
`;
            });

            script += `];

corrections.forEach(correction => {
    if (content.includes(correction.from)) {
        content = content.replace(correction.from, correction.to);
        console.log('✅ 已修复:', correction.from.substring(0, 50) + '...');
    }
});

fs.writeFileSync('PanamaCap-list1.html', content);
console.log('🎉 修复完成！');`;

            scriptContent.value = script;
            scriptDiv.style.display = 'block';
        }

        // 页面加载时显示说明
        window.onload = function() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = `
                <div class="status warning">
                    <h2>📋 使用说明</h2>
                    <p>1. 点击"开始检测所有图片"按钮</p>
                    <p>2. 等待检测完成，查看哪些图片存在</p>
                    <p>3. 点击"生成修复脚本"创建针对性的修复代码</p>
                    <p>4. 将生成的脚本保存为 .js 文件并运行</p>
                </div>
            `;
        };
    </script>
</body>
</html>
