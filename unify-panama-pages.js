// 统一 PanamaCap 页面的脚本
// 这个脚本将基于 BaseballCap-list1.html 的逻辑来统一所有 PanamaCap 页面

const fs = require('fs');
const path = require('path');

// 读取 BaseballCap-list1.html 作为模板
const baseballTemplate = fs.readFileSync('BaseballCap-list1.html', 'utf8');

// 提取关键的 JavaScript 代码段
function extractJavaScriptLogic(content) {
    // 提取购物车功能
    const cartFunctionality = content.match(/\/\/ Cart functionality[\s\S]*?\/\/ 页面加载完成后初始化i18next/);
    
    // 提取产品交互功能
    const productInteraction = content.match(/\/\/ 产品图片点击事件[\s\S]*?\/\/ 页面加载完成后初始化i18next/);
    
    // 提取初始化逻辑
    const initializationLogic = content.match(/\/\/ 页面加载完成后初始化i18next[\s\S]*?\/\/ 页面卸载时清理懒加载监听器[\s\S]*?}\);/);
    
    return {
        cartFunctionality: cartFunctionality ? cartFunctionality[0] : '',
        productInteraction: productInteraction ? productInteraction[0] : '',
        initializationLogic: initializationLogic ? initializationLogic[0] : ''
    };
}

// 生成统一的 JavaScript 代码
function generateUnifiedJavaScript(imagePath) {
    return `
    <script>
        // Cart functionality
        let cart = [];
        const cartButton = document.getElementById('cartButton');
        const cartCount = document.getElementById('cartCount');
        const cartSidebar = document.getElementById('cartSidebar');
        const cartOverlay = document.getElementById('cartOverlay');
        const closeCart = document.getElementById('closeCart');
        const cartItems = document.getElementById('cartItems');
        const cartSubtotal = document.getElementById('cartSubtotal');
        const cartTotal = document.getElementById('cartTotal');
        const checkoutBtn = document.getElementById('checkoutBtn');

        // 产品详情悬浮图框元素
        const productModal = document.getElementById('productModal');
        const modalClose = document.getElementById('modalClose');
        const modalImage = document.getElementById('modalImage');
        const modalTitle = document.getElementById('modalTitle');
        const modalPrice = document.getElementById('modalPrice');
        const modalDescription = document.getElementById('modalDescription');
        const modalTags = document.getElementById('modalTags');
        const modalAddToCart = document.getElementById('modalAddToCart');

        // 从localStorage加载购物车数据
        function loadCart() {
            const savedCart = localStorage.getItem('panamaCap_cart');
            if (savedCart) {
                try {
                    cart = JSON.parse(savedCart);
                    console.log('从localStorage加载购物车数据:', cart.length, '个商品');
                    updateCart();
                } catch (e) {
                    console.error('解析购物车数据出错:', e);
                    cart = [];
                }
            }
        }

        // 保存购物车数据到localStorage
        function saveCart() {
            localStorage.setItem('panamaCap_cart', JSON.stringify(cart));
            console.log('购物车数据已保存，共', cart.length, '个商品');
        }

        // Toggle cart visibility
        cartButton.addEventListener('click', () => {
            loadCart();
            cartSidebar.classList.add('open');
            cartOverlay.classList.add('open');
        });

        closeCart.addEventListener('click', () => {
            cartSidebar.classList.remove('open');
            cartOverlay.classList.remove('open');
        });

        cartOverlay.addEventListener('click', () => {
            cartSidebar.classList.remove('open');
            cartOverlay.classList.remove('open');
        });

        // 产品图片点击事件 - 显示模态框
        let currentProduct = null;
        document.querySelectorAll('.product-image').forEach(img => {
            img.addEventListener('click', async function() {
                const card = this.closest('.product-card');
                const id = card.dataset.id;
                const name = card.dataset.name;
                const price = card.dataset.price;
                const image = card.dataset.image;
                const desc = card.querySelector('[data-i18n^="product_"][data-i18n$="_desc"]');
                const tags = card.querySelectorAll('.tag');

                const basePath = image.replace(/\\.[^/.]+$/, "");

                productModal.classList.add('open');
                document.body.style.overflow = 'hidden';

                modalImage.style.opacity = '0';
                modalImage.style.transition = 'opacity 1s ease-in-out';

                if (typeof window.detectAndPlayModalMedia === 'function') {
                    try {
                        console.log('开始检测产品媒体文件:', basePath);
                        await window.detectAndPlayModalMedia(basePath, modalImage);
                        const finalImageUrl = modalImage.src;
                        currentProduct = {id, name, price, image: finalImageUrl};
                    } catch (error) {
                        console.error('模态框媒体检测出错:', error);
                        const detectedImageUrl = await window.detectModalImage(basePath);
                        modalImage.src = detectedImageUrl;
                        modalImage.style.opacity = '1';
                        currentProduct = {id, name, price, image: detectedImageUrl};
                    }
                } else {
                    console.warn('视频+图片检测功能未加载，使用原始图片检测');
                    const detectedImageUrl = await window.detectModalImage(basePath);
                    modalImage.src = detectedImageUrl;
                    modalImage.style.opacity = '1';
                    currentProduct = {id, name, price, image: detectedImageUrl};
                }
                modalTitle.textContent = name;
                modalPrice.textContent = '$' + price;
                if (desc) modalDescription.textContent = desc.textContent;

                modalTags.innerHTML = '';
                tags.forEach(tag => {
                    const t = document.createElement('span');
                    t.className = 'modal-tag';
                    t.textContent = tag.textContent;
                    modalTags.appendChild(t);
                });
            });
        });

        // 模态框关闭事件
        modalClose.addEventListener('click', () => {
            productModal.classList.remove('open');
            document.body.style.overflow = '';
            currentProduct = null;
        });

        productModal.addEventListener('click', e => {
            if (e.target === productModal) {
                productModal.classList.remove('open');
                document.body.style.overflow = '';
                currentProduct = null;
            }
        });

        // 模态框中的添加到购物车按钮
        modalAddToCart.addEventListener('click', () => {
            if (currentProduct) {
                const item = cart.find(i => i.id === currentProduct.id);
                if (item) item.quantity += 1;
                else cart.push({
                    id: currentProduct.id,
                    name: currentProduct.name,
                    price: parseFloat(currentProduct.price),
                    image: currentProduct.image,
                    quantity: 1
                });

                updateCart();
                const txt = modalAddToCart.innerHTML;
                modalAddToCart.innerHTML = '<i class="fas fa-check mr-2"></i> 已添加到购物车!';
                modalAddToCart.style.backgroundColor = '#28a745';
                setTimeout(() => {
                    modalAddToCart.innerHTML = txt;
                    modalAddToCart.style.backgroundColor = '';
                }, 1500);
            }
        });

        // 产品卡片中的添加到购物车按钮
        document.querySelectorAll('.add-to-cart').forEach(btn => {
            btn.addEventListener('click', function() {
                const card = this.closest('.product-card');
                const id = card.dataset.id;
                const name = card.dataset.name;
                const price = parseFloat(card.dataset.price);
                const image = card.dataset.image;

                const item = cart.find(i => i.id === id);
                if (item) item.quantity += 1;
                else cart.push({id, name, price, image, quantity: 1});

                updateCart();
                cartSidebar.classList.add('open');
                cartOverlay.classList.add('open');

                const added = document.createElement('span');
                added.textContent = 'Added!';
                added.className = 'text-green-600 ml-2';
                this.appendChild(added);
                setTimeout(() => added.remove(), 1000);
            });
        });

        // Update cart display
        function updateCart() {
            const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
            cartCount.textContent = totalItems;

            if (cart.length === 0) {
                const currentLang = localStorage.getItem('selectedLanguage') || 'zh';
                const translationData = window.translations || window.languageData || {};
                let emptyCartText = 'Your cart is empty';
                if (translationData[currentLang] && translationData[currentLang]['cart_empty']) {
                    emptyCartText = translationData[currentLang]['cart_empty'];
                }

                cartItems.innerHTML = \`
                    <div class="text-center py-8 text-gray-500">
                        <i class="fas fa-shopping-cart text-4xl mb-2"></i>
                        <p data-i18n="cart_empty">\${emptyCartText}</p>
                    </div>
                \`;
                checkoutBtn.classList.add('opacity-50');
                checkoutBtn.style.pointerEvents = 'none';
            } else {
                cartItems.innerHTML = '';
                cart.forEach(item => {
                    const itemElement = document.createElement('div');
                    itemElement.className = 'flex items-center py-4 border-b border-gray-200';
                    itemElement.innerHTML = \`
                        <div class="w-20 h-20 bg-gray-100 rounded-lg overflow-hidden mr-4">
                            <img src="\${item.image}" alt="\${item.name}" class="w-full h-full object-cover">
                        </div>
                        <div class="flex-1">
                            <h3 class="font-semibold">\${item.name}</h3>
                            <p class="text-gray-600">$\${item.price.toFixed(2)}</p>
                            <div class="quantity-selector mt-2">
                                <button class="quantity-btn decrease" data-id="\${item.id}">-</button>
                                <input type="number" min="1" value="\${item.quantity}" class="quantity-input" data-id="\${item.id}">
                                <button class="quantity-btn increase" data-id="\${item.id}">+</button>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="font-semibold">$\${(item.price * item.quantity).toFixed(2)}</p>
                            <button class="text-red-500 hover:text-red-700 remove-item mt-1" data-id="\${item.id}">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    \`;
                    cartItems.appendChild(itemElement);
                });

                checkoutBtn.classList.remove('opacity-50');
                checkoutBtn.style.pointerEvents = 'auto';
            }

            const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            cartSubtotal.textContent = \`$\${subtotal.toFixed(2)}\`;
            cartTotal.textContent = \`$\${subtotal.toFixed(2)}\`;

            // Add event listeners to quantity controls
            document.querySelectorAll('.decrease').forEach(button => {
                button.addEventListener('click', function() {
                    const itemId = this.dataset.id;
                    const item = cart.find(item => item.id === itemId);
                    if (item.quantity > 1) {
                        item.quantity -= 1;
                        updateCart();
                    }
                });
            });

            document.querySelectorAll('.increase').forEach(button => {
                button.addEventListener('click', function() {
                    const itemId = this.dataset.id;
                    const item = cart.find(item => item.id === itemId);
                    item.quantity += 1;
                    updateCart();
                });
            });

            document.querySelectorAll('.quantity-input').forEach(input => {
                input.addEventListener('change', function() {
                    const itemId = this.dataset.id;
                    const item = cart.find(item => item.id === itemId);
                    const newQuantity = parseInt(this.value);
                    if (newQuantity >= 1) {
                        item.quantity = newQuantity;
                        updateCart();
                    } else {
                        this.value = item.quantity;
                    }
                });
            });

            document.querySelectorAll('.remove-item').forEach(button => {
                button.addEventListener('click', function() {
                    const itemId = this.dataset.id;
                    const itemIndex = cart.findIndex(item => item.id === itemId);
                    if (itemIndex !== -1) {
                        cart.splice(itemIndex, 1);
                        updateCart();
                    }
                });
            });

            saveCart();
        }

        // Checkout button
        checkoutBtn.addEventListener('click', (e) => {
            if (cart.length === 0) {
                e.preventDefault();
                const currentLang = localStorage.getItem('selectedLanguage') || 'zh';
                const messages = {
                    'zh': '购物车为空，请先添加商品',
                    'en': 'Your cart is empty, please add items first',
                    'es': 'Su carrito está vacío, por favor agregue artículos primero',
                    'fr': 'Votre panier est vide, veuillez d\\'abord ajouter des articles',
                    'de': 'Ihr Warenkorb ist leer, bitte fügen Sie zuerst Artikel hinzu',
                    'ja': 'カートが空です。最初に商品を追加してください',
                    'ko': '장바구니가 비어 있습니다. 먼저 항목을 추가하십시오',
                    'ar': 'سلة التسوق فارغة، يرجى إضافة العناصر أولاً'
                };
                alert(messages[currentLang] || messages['en']);
                return;
            }
            console.log('正在前往结账页面，购物车商品数量:', cart.length);
        });

        // 全局变量用于跟踪懒加载清理函数
        let lazyLoadCleanup = null;

        // 加载更多按钮点击事件
        document.getElementById('loadMore').addEventListener('click', async function() {
            try {
                console.log('用户点击加载更多按钮');
                const result = await window.loadMoreProducts('${imagePath}');
                console.log('加载更多产品结果:', result);
            } catch (error) {
                console.error('加载更多产品失败:', error);
            }
        });

        // 监听自定义事件来处理新产品的模态框显示
        document.addEventListener('showProductModal', async function(event) {
            const { id, name, price, image, description, tags, basePath, subImages } = event.detail;
            currentProduct = { id, name, price, image };
            modalImage.src = image;
            modalTitle.textContent = name;
            modalPrice.textContent = '$' + price;
            modalDescription.textContent = description;

            modalTags.innerHTML = '';
            tags.forEach(tagText => {
                const tag = document.createElement('span');
                tag.className = 'modal-tag';
                tag.textContent = tagText;
                modalTags.appendChild(tag);
            });

            if (subImages && subImages.length > 0) {
                console.log(\`为新产品创建 \${subImages.length} 个缩略图\`);
                const modalContainer = productModal.querySelector('.product-modal') || productModal;
                if (typeof window.createSubImageThumbnails === 'function') {
                    window.createSubImageThumbnails(subImages, modalImage, modalContainer);
                }
            }

            productModal.classList.add('open');
            document.body.style.overflow = 'hidden';
        });

        // 监听自定义事件来处理添加到购物车
        document.addEventListener('addToCart', function(event) {
            const { id, name, price, image, quantity } = event.detail;
            const existingItem = cart.find(item => item.id === id);
            if (existingItem) {
                existingItem.quantity += quantity;
            } else {
                cart.push({ id, name, price, image, quantity });
            }
            updateCart();
            saveCart();
            console.log('商品已添加到购物车:', { id, name, price, quantity });
        });
    </script>
    `;
}

console.log('统一脚本已准备就绪');
