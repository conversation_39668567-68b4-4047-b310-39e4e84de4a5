<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>查找和清理 25-05-29 目录</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .content {
            padding: 30px;
        }
        
        .status {
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border-left: 5px solid;
        }
        
        .status.warning {
            background-color: #fffbeb;
            border-color: #ed8936;
            color: #c05621;
        }
        
        .status.info {
            background-color: #ebf8ff;
            border-color: #3182ce;
            color: #2c5282;
        }
        
        .status.success {
            background-color: #f0fff4;
            border-color: #38a169;
            color: #2f855a;
        }
        
        .example-box {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            margin: 10px;
            transition: transform 0.3s ease;
            font-weight: 500;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 查找和清理 25-05-29 目录</h1>
            <p>定位项目目录并清理无用的说明文件和HTML文件</p>
        </div>
        
        <div class="content">
            <div class="status warning">
                <h2>⚠️ 当前状态</h2>
                <p><strong>问题：</strong> 无法找到 "25-05-29" 目录</p>
                <p><strong>当前位置：</strong> D:\Microsoft VS Code (VS Code 安装目录)</p>
                <p><strong>需要：</strong> 定位到正确的项目目录</p>
            </div>

            <div class="status info">
                <h2>🎯 可能的目录位置</h2>
                <div class="example-box">
                    <h4>25-05-29 可能的含义：</h4>
                    <ul>
                        <li><strong>日期格式：</strong> 2025年5月29日 (25-05-29)</li>
                        <li><strong>版本号：</strong> 版本 25.05.29</li>
                        <li><strong>项目代号：</strong> 特定的项目标识</li>
                    </ul>
                </div>
                
                <div class="example-box">
                    <h4>常见的项目目录位置：</h4>
                    <ul>
                        <li>C:\Users\<USER>\Desktop\25-05-29\</li>
                        <li>C:\Users\<USER>\Documents\25-05-29\</li>
                        <li>C:\Projects\25-05-29\</li>
                        <li>D:\Projects\25-05-29\</li>
                        <li>C:\代码编写\25-05-29\</li>
                    </ul>
                </div>
            </div>

            <div class="status success">
                <h2>🔧 搜索和清理计划</h2>
                
                <h3>第一步：定位项目目录</h3>
                <div class="example-box">
                    <h4>搜索命令：</h4>
                    <div class="code-block">
# 搜索包含产品页面的目录
dir /s /b C:\ | findstr "BaseballCap"
dir /s /b D:\ | findstr "PanamaCap"

# 搜索25-05-29目录
dir /s /b C:\ | findstr "25-05-29"
dir /s /b D:\ | findstr "25-05-29"

# 搜索包含日期的目录
dir /s /b C:\ | findstr "2025"
dir /s /b C:\ | findstr "05-29"
                    </div>
                </div>

                <h3>第二步：检查目录内容</h3>
                <div class="example-box">
                    <h4>一旦找到目录，检查以下文件类型：</h4>
                    <ul>
                        <li>📝 <strong>说明文件：</strong> *.md, *.txt, README.*, CHANGELOG.*</li>
                        <li>🌐 <strong>HTML文件：</strong> 测试页面、临时页面、示例页面</li>
                        <li>📊 <strong>报告文件：</strong> *report*.html, *test*.html, *demo*.html</li>
                        <li>🧪 <strong>测试文件：</strong> test_*.html, example_*.html, sample_*</li>
                    </ul>
                </div>

                <h3>第三步：识别无用文件</h3>
                <div class="example-box">
                    <h4>保留的重要文件：</h4>
                    <ul>
                        <li>✅ <strong>产品页面：</strong> BaseballCap-list*.html, PanamaCap-list*.html</li>
                        <li>✅ <strong>资源文件：</strong> assets/, css/, js/, images/</li>
                        <li>✅ <strong>配置文件：</strong> package.json, config.js</li>
                    </ul>
                    
                    <h4>可能需要删除的文件：</h4>
                    <ul>
                        <li>❌ <strong>临时说明：</strong> temp.md, notes.txt, todo.md</li>
                        <li>❌ <strong>测试页面：</strong> test.html, demo.html, example.html</li>
                        <li>❌ <strong>开发文档：</strong> dev-notes.md, debug.html</li>
                        <li>❌ <strong>备份文件：</strong> *.bak, *.old, *_backup.*</li>
                    </ul>
                </div>
            </div>

            <div class="status info">
                <h2>📋 需要用户提供的信息</h2>
                <div class="example-box">
                    <h4>请提供以下信息以便准确定位：</h4>
                    <ol>
                        <li><strong>项目的完整路径</strong> - 例如：C:\Users\<USER>\Desktop\25-05-29\</li>
                        <li><strong>项目的具体位置</strong> - 桌面、文档、特定文件夹等</li>
                        <li><strong>25-05-29的含义</strong> - 是日期、版本号还是其他标识</li>
                        <li><strong>项目的主要文件</strong> - 确认包含哪些产品页面文件</li>
                    </ol>
                </div>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <h3>🚀 下一步操作</h3>
                <p style="color: #666; font-style: italic;">
                    一旦确定了项目位置，我将：<br>
                    1. 切换到正确的目录<br>
                    2. 扫描所有文件和子目录<br>
                    3. 识别无用的说明文件和HTML文件<br>
                    4. 提供详细的清理建议<br>
                    5. 执行安全的文件清理操作
                </p>
            </div>

            <div class="status warning">
                <h2>⚠️ 重要提醒</h2>
                <ul>
                    <li>🔒 <strong>安全第一：</strong> 清理前会先备份重要文件</li>
                    <li>📋 <strong>详细列表：</strong> 会提供完整的删除文件清单</li>
                    <li>✅ <strong>用户确认：</strong> 删除前需要用户确认</li>
                    <li>🔄 <strong>可恢复：</strong> 重要文件会保留备份</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
