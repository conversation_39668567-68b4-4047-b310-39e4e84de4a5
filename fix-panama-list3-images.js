// 快速修复 PanamaCap-list3.html 中所有产品图片的脚本
// 这个脚本将为所有产品图片添加直接的背景图片设置

const fs = require('fs');

// 读取文件内容
let content = fs.readFileSync('PanamaCap-list3.html', 'utf8');

// 为产品 5-12 添加背景图片
const replacements = [
    {
        from: 'data-image-base="assets/img/cap/panama/list3/5"',
        to: 'data-image-base="assets/img/cap/panama/list3/5" style="background-image: url(\'assets/img/cap/panama/list3/5.jpg\');"'
    },
    {
        from: 'data-image-base="assets/img/cap/panama/list3/6"',
        to: 'data-image-base="assets/img/cap/panama/list3/6" style="background-image: url(\'assets/img/cap/panama/list3/6.jpg\');"'
    },
    {
        from: 'data-image-base="assets/img/cap/panama/list3/7"',
        to: 'data-image-base="assets/img/cap/panama/list3/7" style="background-image: url(\'assets/img/cap/panama/list3/7.jpg\');"'
    },
    {
        from: 'data-image-base="assets/img/cap/panama/list3/8"',
        to: 'data-image-base="assets/img/cap/panama/list3/8" style="background-image: url(\'assets/img/cap/panama/list3/8.jpg\');"'
    },
    {
        from: 'data-image-base="assets/img/cap/panama/list3/9"',
        to: 'data-image-base="assets/img/cap/panama/list3/9" style="background-image: url(\'assets/img/cap/panama/list3/9.jpg\');"'
    },
    {
        from: 'data-image-base="assets/img/cap/panama/list3/10"',
        to: 'data-image-base="assets/img/cap/panama/list3/10" style="background-image: url(\'assets/img/cap/panama/list3/10.jpg\');"'
    },
    {
        from: 'data-image-base="assets/img/cap/panama/list3/11"',
        to: 'data-image-base="assets/img/cap/panama/list3/11" style="background-image: url(\'assets/img/cap/panama/list3/11.jpg\');"'
    },
    {
        from: 'data-image-base="assets/img/cap/panama/list3/12"',
        to: 'data-image-base="assets/img/cap/panama/list3/12" style="background-image: url(\'assets/img/cap/panama/list3/12.jpg\');"'
    }
];

// 应用所有替换
replacements.forEach(replacement => {
    content = content.replace(replacement.from, replacement.to);
});

// 写回文件
fs.writeFileSync('PanamaCap-list3.html', content);

console.log('已为 PanamaCap-list3.html 的所有产品图片添加背景设置');
