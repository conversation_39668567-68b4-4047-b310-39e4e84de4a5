<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终图片测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .product-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .product-card:hover {
            transform: translateY(-5px);
        }
        .product-image {
            height: 200px;
            width: 100%;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            background-color: #f5f5f5;
            border-bottom: 1px solid #eee;
        }
        .product-info {
            padding: 15px;
        }
        .product-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
        }
        .product-price {
            font-size: 18px;
            font-weight: bold;
            color: #4CAF50;
        }
        .status-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: #ccc;
        }
        .status-indicator.success {
            background-color: #4CAF50;
        }
        .status-indicator.error {
            background-color: #f44336;
        }
        .relative {
            position: relative;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Panama Hat List2 最终图片测试</h1>
        <p>测试所有12个产品图片是否能正确显示</p>
        
        <div class="test-grid" id="testGrid">
            <!-- 产品卡片将通过JavaScript动态生成 -->
        </div>
        
        <div style="margin-top: 30px; padding: 20px; background: white; border-radius: 8px;">
            <h3>测试结果统计</h3>
            <div id="testResults">
                <p>正在测试中...</p>
            </div>
        </div>
    </div>

    <script>
        // 产品数据
        const products = [
            { id: 1, name: "Panama Hat Style 2 - 1", price: "89.99" },
            { id: 2, name: "Panama Hat Style 2 - 2", price: "109.99" },
            { id: 3, name: "Panama Hat Style 2 - 3", price: "79.99" },
            { id: 4, name: "Panama Hat Style 2 - 4", price: "95.99" },
            { id: 5, name: "Panama Hat Style 2 - 5", price: "119.99" },
            { id: 6, name: "Panama Hat Style 2 - 6", price: "129.99" },
            { id: 7, name: "Panama Hat Style 2 - 7", price: "69.99" },
            { id: 8, name: "Panama Hat Style 2 - 8", price: "99.99" },
            { id: 9, name: "Panama Hat Style 2 - 9", price: "139.99" },
            { id: 10, name: "Panama Hat Style 2 - 10", price: "89.99" },
            { id: 11, name: "Panama Hat Style 2 - 11", price: "109.99" },
            { id: 12, name: "Panama Hat Style 2 - 12", price: "149.99" }
        ];

        // 测试图片加载
        function testImageLoad(imagePath) {
            return new Promise((resolve) => {
                const img = new Image();
                img.onload = () => resolve({ success: true, path: imagePath });
                img.onerror = () => resolve({ success: false, path: imagePath });
                img.src = imagePath;
            });
        }

        // 创建产品卡片
        function createProductCard(product, imageStatus) {
            const imagePath = `assets/img/cap/panama/list2/${product.id}.jpg`;
            const statusClass = imageStatus.success ? 'success' : 'error';
            
            return `
                <div class="product-card relative">
                    <div class="status-indicator ${statusClass}"></div>
                    <div class="product-image" style="background-image: url('${imagePath}');"></div>
                    <div class="product-info">
                        <div class="product-title">${product.name}</div>
                        <div class="product-price">$${product.price}</div>
                        <div style="font-size: 12px; color: #666; margin-top: 5px;">
                            状态: ${imageStatus.success ? '✅ 加载成功' : '❌ 加载失败'}
                        </div>
                    </div>
                </div>
            `;
        }

        // 执行测试
        async function runTest() {
            const testGrid = document.getElementById('testGrid');
            const testResults = document.getElementById('testResults');
            
            let successCount = 0;
            let totalCount = products.length;
            
            console.log('开始测试所有产品图片...');
            
            for (const product of products) {
                const imagePath = `assets/img/cap/panama/list2/${product.id}.jpg`;
                console.log(`测试图片: ${imagePath}`);
                
                const imageStatus = await testImageLoad(imagePath);
                if (imageStatus.success) {
                    successCount++;
                }
                
                const cardHTML = createProductCard(product, imageStatus);
                testGrid.innerHTML += cardHTML;
                
                console.log(`产品 ${product.id} 测试结果:`, imageStatus);
            }
            
            // 更新测试结果
            const successRate = ((successCount / totalCount) * 100).toFixed(1);
            testResults.innerHTML = `
                <p><strong>测试完成！</strong></p>
                <p>总计: ${totalCount} 个图片</p>
                <p>成功: ${successCount} 个</p>
                <p>失败: ${totalCount - successCount} 个</p>
                <p>成功率: ${successRate}%</p>
                ${successCount === totalCount ? 
                    '<p style="color: green; font-weight: bold;">🎉 所有图片都加载成功！</p>' : 
                    '<p style="color: red; font-weight: bold;">⚠️ 部分图片加载失败，请检查文件路径。</p>'
                }
            `;
            
            console.log(`测试完成: ${successCount}/${totalCount} 成功`);
        }

        // 页面加载完成后开始测试
        document.addEventListener('DOMContentLoaded', runTest);
    </script>
</body>
</html>
