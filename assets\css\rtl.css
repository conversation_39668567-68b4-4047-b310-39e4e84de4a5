/* RTL支持样式 - 主要用于阿拉伯语等从右到左的语言 */

body.rtl {
  direction: rtl;
  text-align: right;
}

body.rtl .header .logo {
  margin-right: 0;
  margin-left: 30px;
}

body.rtl .navmenu {
  padding-right: 0;
}

body.rtl .navmenu ul {
  padding-right: 0;
}

body.rtl .navmenu .dropdown .toggle-dropdown {
  margin-right: 5px;
  margin-left: 0;
}

body.rtl .mobile-nav-toggle {
  right: auto;
  left: 15px;
}

body.rtl .hero-content {
  text-align: right;
}

body.rtl .btn-get-started {
  margin-right: 0;
  margin-left: 15px;
}

body.rtl .btn-watch-video i {
  margin-right: 0;
  margin-left: 5px;
}

body.rtl .footer .footer-contact {
  text-align: right;
}

body.rtl .footer .footer-links ul i {
  padding-right: 0;
  padding-left: 10px;
}

body.rtl .footer .footer-newsletter form input[type="email"] {
  padding: 10px 15px 10px 35px;
}

body.rtl .footer .footer-newsletter form button {
  right: auto;
  left: 0;
}

/* 分屏部分的RTL支持 */
body.rtl .about-section h1 {
  left: auto;
  right: 50%;
  transform: translateX(50%);
}

body.rtl .btn {
  left: auto;
  right: 50%;
  transform: translateX(50%);
}

/* 确保在RTL模式下视频和遮罩层正确显示 */
body.rtl .split.left video,
body.rtl .split.right video,
body.rtl .video-overlay,
body.rtl .baseball-video-overlay {
  left: auto;
  right: 0;
}