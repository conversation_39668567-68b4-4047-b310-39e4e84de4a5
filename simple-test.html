<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单图片测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .product-image {
            width: 200px;
            height: 200px;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            border: 2px solid #ddd;
            border-radius: 8px;
            margin: 10px;
            display: inline-block;
            background-color: #f5f5f5;
        }
        .image-info {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Panama Hat List2 图片显示测试</h1>
        
        <div class="test-section">
            <h2>方法1：直接使用 img 标签</h2>
            <img src="assets/img/cap/panama/list2/1.jpg" alt="帽子1" style="width: 200px; height: 200px; object-fit: cover; border-radius: 8px; margin: 10px;">
            <img src="assets/img/cap/panama/list2/2.jpg" alt="帽子2" style="width: 200px; height: 200px; object-fit: cover; border-radius: 8px; margin: 10px;">
            <img src="assets/img/cap/panama/list2/3.jpg" alt="帽子3" style="width: 200px; height: 200px; object-fit: cover; border-radius: 8px; margin: 10px;">
        </div>
        
        <div class="test-section">
            <h2>方法2：使用背景图片（类似产品页面）</h2>
            <div class="product-image" data-image-base="assets/img/cap/panama/list2/1">
                <div class="image-info">图片1 - 背景图片方式</div>
            </div>
            <div class="product-image" data-image-base="assets/img/cap/panama/list2/2">
                <div class="image-info">图片2 - 背景图片方式</div>
            </div>
            <div class="product-image" data-image-base="assets/img/cap/panama/list2/3">
                <div class="image-info">图片3 - 背景图片方式</div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>方法3：直接设置背景图片</h2>
            <div class="product-image" style="background-image: url('assets/img/cap/panama/list2/4.jpg');">
                <div class="image-info">图片4 - 直接CSS</div>
            </div>
            <div class="product-image" style="background-image: url('assets/img/cap/panama/list2/5.jpg');">
                <div class="image-info">图片5 - 直接CSS</div>
            </div>
            <div class="product-image" style="background-image: url('assets/img/cap/panama/list2/6.jpg');">
                <div class="image-info">图片6 - 直接CSS</div>
            </div>
        </div>
    </div>

    <script>
        // 为方法2设置背景图片
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，开始设置背景图片');
            
            document.querySelectorAll('.product-image[data-image-base]').forEach(function(element, index) {
                const basePath = element.getAttribute('data-image-base');
                if (basePath) {
                    const imageUrl = basePath + '.jpg';
                    console.log(`设置图片 ${index + 1}:`, imageUrl);
                    element.style.backgroundImage = `url('${imageUrl}')`;
                    
                    // 测试图片是否能加载
                    const testImg = new Image();
                    testImg.onload = function() {
                        console.log('图片加载成功:', imageUrl);
                        element.style.border = '2px solid green';
                    };
                    testImg.onerror = function() {
                        console.error('图片加载失败:', imageUrl);
                        element.style.border = '2px solid red';
                        element.innerHTML = '<div style="padding: 20px; text-align: center; color: red;">图片加载失败</div>';
                    };
                    testImg.src = imageUrl;
                }
            });
        });
    </script>
</body>
</html>
