// 修复 PanamaCap-list1-corrected.html 中的路径问题
const fs = require('fs');

console.log('🔄 开始修复路径问题...');

// 读取文件
let content = fs.readFileSync('D:\\Users\\dp\\Desktop\\编写帽子网站\\帽子网站编写\\代码编写\\25-05-29\\PanamaCap-list1-corrected.html', 'utf8');

console.log('✅ 已读取文件');

// 进行路径替换
const replacements = [
    // 页面标题
    {
        from: '<h1 class="text-xl font-bold text-white" data-i18n="baseball_caps_collection">Baseball Caps Collection</h1>',
        to: '<h1 class="text-xl font-bold text-white" data-i18n="panama_hats_collection">Panama Hats Collection</h1>'
    },
    // 页脚
    {
        from: '<p data-i18n="footer_copyright_text">2023 Baseball Caps Collection. All rights reserved.</p>',
        to: '<p data-i18n="footer_copyright_text">2023 Panama Hats Collection. All rights reserved.</p>'
    },
    // 所有 Baseball 路径替换为 panama
    {
        from: /assets\/img\/cap\/Baseball\/list1/g,
        to: 'assets/img/cap/panama/list1'
    },
    // 确保所有 data-image 属性正确
    {
        from: /data-image="assets\/img\/cap\/Baseball\/list1/g,
        to: 'data-image="assets/img/cap/panama/list1'
    },
    // 确保所有 data-image-base 属性正确
    {
        from: /data-image-base="assets\/img\/cap\/Baseball\/list1/g,
        to: 'data-image-base="assets/img/cap/panama/list1'
    }
];

console.log('🔧 开始应用替换...');

let totalReplacements = 0;
replacements.forEach((replacement, index) => {
    const beforeContent = content;
    if (replacement.from instanceof RegExp) {
        const matches = content.match(replacement.from);
        content = content.replace(replacement.from, replacement.to);
        if (matches) {
            totalReplacements += matches.length;
            console.log(`✅ 替换 ${index + 1}: 找到并替换了 ${matches.length} 个匹配项`);
        } else {
            console.log(`⚠️  替换 ${index + 1}: 未找到匹配项`);
        }
    } else {
        if (content.includes(replacement.from)) {
            content = content.replace(replacement.from, replacement.to);
            totalReplacements++;
            console.log(`✅ 替换 ${index + 1}: 成功`);
        } else {
            console.log(`⚠️  替换 ${index + 1}: 未找到匹配项`);
        }
    }
});

// 写入文件
fs.writeFileSync('D:\\Users\\dp\\Desktop\\编写帽子网站\\帽子网站编写\\代码编写\\25-05-29\\PanamaCap-list1-final.html', content);

console.log(`\n🎉 修复完成！`);
console.log(`   - 输出文件: PanamaCap-list1-final.html`);
console.log(`   - 总共替换了 ${totalReplacements} 处`);
console.log(`   - 文件大小: ${Math.round(content.length / 1024)} KB`);

console.log('\n📋 修复内容:');
console.log('   ✅ 页面标题改为 Panama Hats Collection');
console.log('   ✅ 页脚改为 Panama Hats Collection');
console.log('   ✅ 所有图片路径改为 assets/img/cap/panama/list1');
console.log('   ✅ 所有产品数据属性路径已更新');

console.log('\n🚀 现在可以:');
console.log('   1. 将 PanamaCap-list1-final.html 重命名为 PanamaCap-list1.html');
console.log('   2. 替换原有文件');
console.log('   3. 测试页面功能');
