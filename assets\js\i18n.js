// i18n.js - 集中管理国际化功能

// 确保translations对象存在
if (typeof translations === 'undefined') {
  console.error('translations对象未定义，请确保先加载languages.js');
  // 创建一个空的translations对象，防止报错
  var translations = {
    en: {}, zh: {}, es: {}, ar: {}, fr: {}, ru: {}, pt: {}, de: {}, ja: {}, hi: {}
  };
}

// 打印translations对象，检查是否正确加载
console.log('translations对象:', translations);
console.log('阿拉伯语翻译示例:', translations.ar.load_more, translations.ar.add_to_cart);

// 导入现有的翻译数据
const translationResources = {
  en: {
    translation: translations.en
  },
  zh: {
    translation: translations.zh
  },
  es: {
    translation: translations.es
  },
  ar: {
    translation: translations.ar
  },
  fr: {
    translation: translations.fr
  },
  ru: {
    translation: translations.ru
  },
  pt: {
    translation: translations.pt
  },
  de: {
    translation: translations.de
  },
  ja: {
    translation: translations.ja
  },
  hi: {
    translation: translations.hi
  }
};

// 初始化i18next
document.addEventListener('DOMContentLoaded', function() {
  // 从localStorage获取选择的语言，默认为中文
  const savedLanguage = localStorage.getItem('selectedLanguage') || 'zh';

  // 初始化i18next
  i18next
    .init({
      lng: savedLanguage,
      debug: true,
      resources: translationResources,
      fallbackLng: 'en',
      interpolation: {
        escapeValue: false // 不转义HTML
      }
    })
    .then(function(t) {
      // 初始化完成后更新页面
      updateContent();

      // 设置RTL支持
      setRTLSupport(savedLanguage);

      // 更新语言选择器显示
      updateLanguageSelector(savedLanguage);

      console.log('i18next初始化完成，当前语言：', savedLanguage);
    });

  // 添加语言切换事件监听
  setupLanguageSwitcher();
});

// 更新页面内容
function updateContent() {
  console.log('开始更新页面内容，当前语言：', i18next.language);

  // 记录所有需要翻译的元素
  const elementsWithDataI18n = document.querySelectorAll('[data-i18n]');
  console.log('找到', elementsWithDataI18n.length, '个带有data-i18n属性的元素');

  // 检查翻译资源是否正确加载
  console.log('当前翻译资源:', i18next.options.resources);
  console.log('当前语言:', i18next.language);

  // 检查几个关键翻译是否存在
  console.log('add_to_cart翻译:', i18next.t('add_to_cart'));
  console.log('product_1_title翻译:', i18next.t('product_1_title'));
  console.log('our_collection翻译:', i18next.t('our_collection'));

  // 更新所有带有data-i18n属性的元素
  elementsWithDataI18n.forEach(element => {
    const key = element.getAttribute('data-i18n');
    const originalText = element.textContent;

    if (i18next.exists(key)) {
      const translation = i18next.t(key);
      element.textContent = translation;
      console.log('翻译元素:', key, '[', originalText, '] -> [', translation, ']');
    } else {
      console.warn('找不到翻译键:', key, '原文本:', originalText);
    }
  });

  // 更新所有带有data-i18n-placeholder属性的元素
  document.querySelectorAll('[data-i18n-placeholder]').forEach(element => {
    const key = element.getAttribute('data-i18n-placeholder');
    if (i18next.exists(key)) {
      element.placeholder = i18next.t(key);
      console.log('翻译placeholder:', key, '->', i18next.t(key));
    } else {
      console.warn('找不到placeholder翻译键:', key);
    }
  });

  // 更新所有带有data-i18n-title属性的元素
  document.querySelectorAll('[data-i18n-title]').forEach(element => {
    const key = element.getAttribute('data-i18n-title');
    if (i18next.exists(key)) {
      element.title = i18next.t(key);
      console.log('翻译title:', key, '->', i18next.t(key));
    } else {
      console.warn('找不到title翻译键:', key);
    }
  });

  // 更新所有带有data-i18n-html属性的元素（包含HTML的翻译）
  document.querySelectorAll('[data-i18n-html]').forEach(element => {
    const key = element.getAttribute('data-i18n-html');
    if (i18next.exists(key)) {
      element.innerHTML = i18next.t(key);
      console.log('翻译HTML:', key, '->', i18next.t(key));
    } else {
      console.warn('找不到HTML翻译键:', key);
    }
  });

  // 特别检查所有"Add to Cart"按钮
  document.querySelectorAll('.add-to-cart span').forEach(span => {
    const key = span.getAttribute('data-i18n');
    console.log('检查购物车按钮:', span.textContent, 'data-i18n=', key);

    // 确保使用正确的翻译键
    if (key !== 'add_to_cart') {
      span.setAttribute('data-i18n', 'add_to_cart');
      console.log('修正购物车按钮属性为add_to_cart');
    }

    // 强制更新文本
    if (i18next.exists('add_to_cart')) {
      span.textContent = i18next.t('add_to_cart');
      console.log('强制更新购物车按钮文本:', i18next.t('add_to_cart'));
    }
  });

  console.log('页面内容已更新完成');
}

// 设置RTL支持
function setRTLSupport(language) {
  if (language === 'ar') {
    document.body.classList.add('rtl');
    document.documentElement.dir = 'rtl';
  } else {
    document.body.classList.remove('rtl');
    document.documentElement.dir = 'ltr';
  }

  console.log('RTL支持已设置，当前语言：', language);
}

// 更新语言选择器显示
function updateLanguageSelector(language) {
  const currentLanguageElement = document.getElementById('current-language');
  if (currentLanguageElement) {
    currentLanguageElement.textContent = language.toUpperCase();
  }

  console.log('语言选择器已更新，当前语言：', language);
}

// 设置语言切换器
function setupLanguageSwitcher() {
  document.querySelectorAll('#language-menu a, [data-lang]').forEach(link => {
    link.addEventListener('click', function(e) {
      e.preventDefault();
      const lang = this.getAttribute('data-lang');

      // 保存语言选择到localStorage
      localStorage.setItem('selectedLanguage', lang);

      // 切换i18next语言
      i18next.changeLanguage(lang).then(() => {
        // 更新页面内容
        updateContent();

        // 设置RTL支持
        setRTLSupport(lang);

        // 更新语言选择器显示
        updateLanguageSelector(lang);

        console.log('语言已切换为：', lang);
      });
    });
  });

  console.log('语言切换器已设置');
}

// 导出函数供其他页面使用
window.i18nHelper = {
  updateContent,
  setRTLSupport,
  updateLanguageSelector,
  setupLanguageSwitcher,
  // 添加一个手动初始化函数，供其他页面调用
  manualInit: function(language) {
    console.log('手动初始化i18next，语言:', language);

    // 设置HTML语言属性
    document.documentElement.setAttribute('lang', language);

    // 如果i18next已经初始化，直接切换语言
    if (i18next.isInitialized) {
      console.log('i18next已初始化，切换语言');
      i18next.changeLanguage(language).then(() => {
        updateContent();
        setRTLSupport(language);
        updateLanguageSelector(language);
      });
    } else {
      // 否则初始化i18next
      console.log('i18next未初始化，开始初始化');
      i18next
        .init({
          lng: language,
          debug: true,
          resources: translationResources,
          fallbackLng: 'en',
          interpolation: {
            escapeValue: false // 不转义HTML
          }
        })
        .then(function(t) {
          updateContent();
          setRTLSupport(language);
          updateLanguageSelector(language);
          console.log('i18next手动初始化完成');
        });
    }
  }
};
