<script type="text/javascript">
        var gk_isXlsx = false;
        var gk_xlsxFileLookup = {};
        var gk_fileData = {};
        function filledCell(cell) {
          return cell !== '' && cell != null;
        }
        function loadFileData(filename) {
        if (gk_isXlsx && gk_xlsxFileLookup[filename]) {
            try {
                var workbook = XLSX.read(gk_fileData[filename], { type: 'base64' });
                var firstSheetName = workbook.SheetNames[0];
                var worksheet = workbook.Sheets[firstSheetName];

                // Convert sheet to JSON to filter blank rows
                var jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, blankrows: false, defval: '' });
                // Filter out blank rows (rows where all cells are empty, null, or undefined)
                var filteredData = jsonData.filter(row => row.some(filledCell));

                // Heuristic to find the header row by ignoring rows with fewer filled cells than the next row
                var headerRowIndex = filteredData.findIndex((row, index) =>
                  row.filter(filledCell).length >= filteredData[index + 1]?.filter(filledCell).length
                );
                // Fallback
                if (headerRowIndex === -1 || headerRowIndex > 25) {
                  headerRowIndex = 0;
                }

                // Convert filtered JSON back to CSV
                var csv = XLSX.utils.aoa_to_sheet(filteredData.slice(headerRowIndex)); // Create a new sheet from filtered array of arrays
                csv = XLSX.utils.sheet_to_csv(csv, { header: 1 });
                return csv;
            } catch (e) {
                console.error(e);
                return "";
            }
        }
        return gk_fileData[filename] || "";
        }
        </script><!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-i18n="our_collection_title">Our Collection - Panama Hats</title>
    <!-- Tailwind CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- 语言支持 -->
    <script src="assets/js/languages.js"></script>
    <!-- i18next库 -->
    <script src="https://cdn.jsdelivr.net/npm/i18next@21.6.10/i18next.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/i18next-browser-languagedetector@6.1.3/i18nextBrowserLanguageDetector.min.js"></script>
    <!-- 自定义i18n实现 -->
    <script src="assets/js/i18n.js"></script>
    <!-- 立即设置语言 -->
    <script>
        // 立即设置HTML语言属性
        (function() {
            // 检查URL参数中是否有语言设置
            const urlParams = new URLSearchParams(window.location.search);
            const langParam = urlParams.get('lang');

            // 如果URL中有语言参数，使用它；否则使用localStorage中的语言或默认语言
            const savedLanguage = langParam || localStorage.getItem('selectedLanguage') || 'zh';

            // 如果URL中有语言参数，保存到localStorage
            if (langParam) {
                localStorage.setItem('selectedLanguage', langParam);
                console.log('从URL参数设置语言为:', langParam);
            }

            document.documentElement.setAttribute('lang', savedLanguage);
            console.log('立即设置HTML lang属性为:', savedLanguage);

            // 如果是阿拉伯语，设置RTL
            if (savedLanguage === 'ar') {
                document.documentElement.dir = 'rtl';
                document.body.classList.add('rtl');
            } else {
                document.documentElement.dir = 'ltr';
                document.body.classList.remove('rtl');
            }

            // 定义一个全局函数，用于直接应用翻译
            window.applyTranslations = function() {
                const currentLang = localStorage.getItem('selectedLanguage') || 'zh';
                console.log('直接应用翻译，当前语言:', currentLang);

                // 确保translations对象存在
                if (typeof translations === 'undefined' || !translations[currentLang]) {
                    console.error('translations对象不存在或当前语言没有翻译数据');
                    return;
                }

                // 直接应用翻译到所有带有data-i18n属性的元素
                document.querySelectorAll('[data-i18n]').forEach(element => {
                    const key = element.getAttribute('data-i18n');
                    if (translations[currentLang][key]) {
                        element.textContent = translations[currentLang][key];
                        console.log('直接翻译元素:', key, '->', translations[currentLang][key]);
                    } else {
                        console.warn('找不到直接翻译键:', key);
                    }
                });

                // 特别处理"Add to Cart"按钮
                document.querySelectorAll('.add-to-cart span').forEach(span => {
                    if (translations[currentLang]['add_to_cart']) {
                        span.textContent = translations[currentLang]['add_to_cart'];
                        console.log('直接翻译购物车按钮:', translations[currentLang]['add_to_cart']);
                    }
                });
            };

            // 页面加载完成后应用翻译
            document.addEventListener('DOMContentLoaded', function() {
                // 延迟执行，确保所有元素都已加载
                setTimeout(function() {
                    window.applyTranslations();

                    document.querySelectorAll('.product-card .flex-wrap').forEach(t=>t.classList.add('hidden'));
                    document.querySelectorAll('.product-card .flex.justify-between.mt-auto').forEach(b=>{
                        b.classList.remove('pt-4');b.classList.add('pt-2');
                        const c=b.querySelector('.btn-primary');
                        if(c){
                            c.classList.remove('px-4','py-2','text-sm');
                            c.classList.add('px-2','py-1','text-xs');
                            const i=c.querySelector('.mr-2');
                            if(i){i.classList.remove('mr-2');i.classList.add('mr-1');}
                        }
                        const f=b.querySelector('.bg-black');
                        if(f){
                            f.classList.remove('px-3','py-2');
                            f.classList.add('px-2','py-1','text-xs');
                        }
                    });
                }, 100);
            });
        })();
    </script>
    <style>
        :root {
            --primary-color: #4CAF50;
            --primary-hover: #45a049;
            --secondary-color: #f8f9fa;
            --accent-color: #ff9800;
        }

        /* 动态背景样式 */
        .dynamic-background {
            color: #f0f0f0;
            background: #101522;
            background-blend-mode: hard-light;
            background-image: radial-gradient(circle at 20% 20%, #ffcc7066 10%, #ffcc7000 50%),
                             radial-gradient(circle at 80% 80%, #0033ff66 10%, #0033ff00 50%),
                             radial-gradient(ellipse at 35% 70%, #00ff4866 10%, #00ff4800 50%),
                             radial-gradient(ellipse at 70% 35%, #ff005d66 10%, #ff005d00 60%);
            background-size: 250% 250%;
            animation: background-animation 30s infinite;
            position: relative;
        }

        .dynamic-background::after {
            content: "";
            width: 100%;
            height: 100%;
            position: fixed;
            top: 0;
            left: 0;
            backdrop-filter: blur(4px);
            background: radial-gradient(ellipse, #00000000, #000000cc);
            z-index: -1;
            pointer-events: none;
        }

        @keyframes background-animation {
            0% {
                background-position: 5% 0%;
            }
            25% {
                background-position: 20% 80%;
            }
            50% {
                background-position: 96% 100%;
            }
            75% {
                background-position: 80% 10%;
            }
            100% {
                background-position: 5% 0%;
            }
        }

        .product-card {
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.2);
            backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            flex-direction: column;
        }

        .product-card .p-4{padding:.75rem!important}
        .product-card h3{font-size:.875rem!important;line-height:1.25!important}
        .product-card p{font-size:.75rem!important;margin-bottom:.5rem!important}
        .product-card .btn-primary{padding:.375rem .75rem!important;font-size:.75rem!important}
        .product-card .price-tag{font-size:.75rem!important;padding:.125rem .375rem!important}

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .product-image {
            aspect-ratio: 1/1; /* 设置为正方形 */
            height: 200px; /* 设置明确的高度 */
            width: 100%; /* 设置宽度为100% */
            background-size: cover; /* 背景图片覆盖整个容器 */
            background-position: center; /* 背景图片居中 */
            background-repeat: no-repeat; /* 不重复背景图片 */
            transition: transform 0.5s ease; /* 悬浮时的变换效果 */
            cursor: pointer; /* 添加指针样式表明可点击 */
            border-radius: 8px; /* 添加圆角 */
            background-color: #f0f0f0; /* 添加背景色，图片未加载时显示 */
        }

        .product-card:hover .product-image {
            transform: scale(1.05);
        }

        .tag {
            transition: all 0.2s ease;
            background-color: rgba(255, 255, 255, 0.2);
            color: #f0f0f0;
        }

        .tag:hover {
            background-color: var(--primary-color);
            color: white;
            transform: scale(1.05);
        }

        .btn-primary {
            background-color: var(--primary-color);
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background-color: var(--primary-hover);
            transform: translateY(-2px);
        }

        .price-tag {
            background-color: var(--primary-color);
            color: white;
            transform: rotate(-5deg);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: rotate(-5deg) scale(1); }
            50% { transform: rotate(-5deg) scale(1.05); }
            100% { transform: rotate(-5deg) scale(1); }
        }

        .discount-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: #f44336;
            color: white;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
        }

        .loading-spinner {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Cart sidebar styles */
        .cart-sidebar {
            transform: translateX(100%);
            transition: transform 0.3s ease-out;
            background-color: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(10px);
            border-left: 1px solid rgba(255, 255, 255, 0.2);
        }

        .cart-sidebar.open {
            transform: translateX(0);
        }

        .cart-overlay {
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
            background-color: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(3px);
        }

        .cart-overlay.open {
            opacity: 1;
            visibility: visible;
        }

        /* Quantity selector */
        .quantity-selector {
            display: flex;
            align-items: center;
        }

        .quantity-btn {
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(0, 0, 0, 0.5);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
            cursor: pointer;
            font-size: 16px;
            transition: all 0.2s ease;
        }

        .quantity-btn:hover {
            background-color: rgba(0, 0, 0, 0.7);
            border-color: rgba(255, 255, 255, 0.4);
        }

        .quantity-input {
            width: 40px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.3);
            background-color: rgba(0, 0, 0, 0.3);
            color: white;
            margin: 0 5px;
            padding: 5px;
        }

        /* Back button style */
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
        }
        .back-button .btn-back {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background-color: rgba(0, 0, 0, 0.6);
            border-radius: 50%;
            box-shadow: 0 2px 5px rgba(0,0,0,0.3);
            color: #fff;
            text-decoration: none;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        .back-button .btn-back:hover {
            background-color: rgba(0, 0, 0, 0.8);
            transform: scale(1.1);
            border-color: rgba(255, 255, 255, 0.5);
        }

        /* 语言选择器样式 */
        .language-dropdown {
            position: relative;
            display: inline-block;
        }

        .language-dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            background-color: rgba(0, 0, 0, 0.8);
            min-width: 160px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.5);
            z-index: 1000;
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .language-dropdown-content a {
            color: white;
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            transition: all 0.2s ease;
        }

        .language-dropdown-content a:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .language-dropdown:hover .language-dropdown-content {
            display: block;
        }

        .language-btn {
            background-color: transparent;
            color: white;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
        }

        /* RTL支持 */
        body.rtl {
            direction: rtl;
            text-align: right;
        }

        body.rtl .back-button {
            left: auto;
            right: 20px;
        }

        .modal-overlay{position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,.8);display:flex;align-items:center;justify-content:center;z-index:1000;opacity:0;visibility:hidden;transition:opacity .3s ease,visibility .3s ease;backdrop-filter:blur(5px)}
        .modal-overlay.open{opacity:1;visibility:visible}
        .product-modal{display:flex;width:90%;max-width:1000px;max-height:80vh;background-color:rgba(30,30,30,.9);border-radius:12px;overflow:hidden;box-shadow:0 10px 30px rgba(0,0,0,.5);border:1px solid rgba(255,255,255,.2);animation:modal-appear .3s ease}
        @keyframes modal-appear{from{transform:scale(.9);opacity:0}to{transform:scale(1);opacity:1}}
        .modal-image-container{flex:2;padding:20px;display:flex;align-items:center;justify-content:center}
        .modal-image{width:100%;height:100%;object-fit:contain;border-radius:8px}
        .modal-content{flex:1;padding:30px;display:flex;flex-direction:column;color:#f0f0f0;border-left:1px solid rgba(255,255,255,.1)}
        .modal-title{font-size:24px;font-weight:700;margin-bottom:15px}
        .modal-price{font-size:22px;font-weight:700;color:var(--primary-color);margin-bottom:20px}
        .modal-description{margin-bottom:20px;line-height:1.6}
        .modal-tags{display:flex;flex-wrap:wrap;gap:8px;margin-bottom:30px}
        .modal-tag{background-color:rgba(255,255,255,.2);color:#f0f0f0;padding:5px 10px;border-radius:4px;font-size:12px;transition:all .2s ease}
        .modal-tag:hover{background-color:var(--primary-color);color:#fff}
        .modal-add-to-cart{margin-top:auto;background-color:var(--primary-color);color:#fff;border:none;padding:12px 20px;border-radius:8px;font-weight:700;cursor:pointer;transition:all .3s ease;display:flex;align-items:center;justify-content:center}
        .modal-add-to-cart:hover{background-color:var(--primary-hover);transform:translateY(-2px)}
        .modal-close{position:absolute;top:15px;right:15px;background-color:rgba(0,0,0,.5);color:#fff;border:none;width:30px;height:30px;border-radius:50%;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .2s ease;border:1px solid rgba(255,255,255,.2)}
        .modal-close:hover{background-color:rgba(0,0,0,.8);transform:scale(1.1)}
        @media (max-width:768px){.product-modal{flex-direction:column;max-height:90vh}.modal-image-container{height:50vh;flex:none}.modal-content{flex:none;border-left:none;border-top:1px solid rgba(255,255,255,.1)}}
    </style>
    <!-- 引入通用的图片检测功能 -->
    <script src="assets/js/main.js"></script>
</head>
<body class="antialiased dynamic-background">
    <!-- Back button -->
    <div class="back-button">
        <a href="./PanamaCap.html" class="btn-back">
            <i class="fas fa-arrow-left"></i>
        </a>
    </div>

    <!-- Minimal Header -->
    <header class="bg-black bg-opacity-70 shadow-sm py-4 backdrop-filter backdrop-blur-md">
        <div class="container mx-auto px-4 flex justify-between items-center">
            <h1 class="text-xl font-bold text-white" data-i18n="panama_hats_collection">Panama Hats Collection</h1>
            <div class="flex items-center space-x-4">
                <a href="#" class="text-gray-300 hover:text-white">
                    <i class="fas fa-search"></i>
                </a>
                <button id="cartButton" class="text-gray-300 hover:text-white relative">
                    <i class="fas fa-shopping-cart"></i>
                    <span id="cartCount" class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">0</span>
                </button>
                <!-- 语言选择菜单已移除 -->
            </div>
        </div>
    </header>

    <!-- Products Section -->
    <section class="py-8">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center mb-8">
                <h2 class="text-2xl font-bold text-white" data-i18n="our_collection_title">Our Collection</h2>
                <div class="flex space-x-2">
                    <button class="px-4 py-2 bg-black bg-opacity-50 text-white rounded-lg shadow-sm hover:bg-opacity-70 transition"><span data-i18n="filter">Filter</span> <i class="fas fa-filter ml-2"></i></button>
                    <button class="px-4 py-2 bg-black bg-opacity-50 text-white rounded-lg shadow-sm hover:bg-opacity-70 transition"><span data-i18n="sort">Sort</span> <i class="fas fa-sort ml-2"></i></button>
                </div>
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                <!-- Product 1 -->
                <div class="product-card animate__animated animate__fadeIn" data-id="1" data-name="Ossimu Quasi Alum" data-price="89.99" data-image="assets/img/cap/panama/list1/1.jpg">
                    <div class="relative overflow-hidden">
                        <div class="product-image" data-image-base="assets/img/cap/panama/list1/1" style="background-image: url('assets/img/cap/panama/list1/1.jpg');"></div>
                        <div class="discount-badge px-2 py-1 rounded text-xs font-bold" data-i18n="discount_badge">-20%</div>
                    </div>
                    <div class="p-4 flex-grow flex flex-col">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-lg font-semibold" data-i18n="product_1_title">Ossimu Quasi Alum</h3>
                            <div class="price-tag px-2 py-1 rounded text-sm font-bold">$89.99</div>
                        </div>
                        <p class="text-gray-200 text-sm mb-3" data-i18n="product_1_desc">Lightweight and breathable for sunny days.</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_1_1">Delect a Distinctio</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_1_2">Facere</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_1_3">Morbi Leo</span>
                        </div>
                        <div class="flex justify-between mt-auto pt-2">
                            <button class="btn-primary px-2 py-1 rounded-lg text-xs font-semibold flex items-center add-to-cart">
                                <i class="fas fa-shopping-cart mr-1"></i> <span data-i18n="add_to_cart">Add to Cart</span>
                            </button>
                            <button class="bg-black bg-opacity-50 hover:bg-opacity-70 text-white px-2 py-1 rounded-lg transition border border-gray-600 text-xs">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Product 2 -->
                <div class="product-card animate__animated animate__fadeIn" data-id="2" data-name="Deleniti Quidem Labori" data-price="109.99" data-image="assets/img/cap/panama/list1/2.jpg">
                    <div class="relative overflow-hidden">
                        <div class="product-image" data-image-base="assets/img/cap/panama/list1/2" style="background-image: url('assets/img/cap/panama/list1/2.jpg');"></div>
                    </div>
                    <div class="p-4 flex-grow flex flex-col">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-lg font-semibold" data-i18n="product_2_title">Deleniti Quidem Labori</h3>
                            <div class="price-tag px-2 py-1 rounded text-sm font-bold">$109.99</div>
                        </div>
                        <p class="text-gray-200 text-sm mb-3" data-i18n="product_2_desc">Sophisticated style for formal occasions.</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_2_1">Volutpat</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_2_2">Scelerisque</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_2_3">Turpis Galo</span>
                        </div>
                        <div class="flex justify-between mt-auto pt-4">
                            <button class="btn-primary px-4 py-2 rounded-lg text-sm font-semibold flex items-center add-to-cart">
                                <i class="fas fa-shopping-cart mr-2"></i> <span data-i18n="add_to_cart">Add to Cart</span>
                            </button>
                            <button class="bg-black bg-opacity-50 hover:bg-opacity-70 text-white px-3 py-2 rounded-lg transition border border-gray-600">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Product 3 -->
                <div class="product-card animate__animated animate__fadeIn" data-id="3" data-name="Et Mestaci" data-price="79.99" data-image="assets/img/cap/panama/list1/3.jpg">
                    <div class="relative overflow-hidden">
                        <div class="product-image" data-image-base="assets/img/cap/panama/list1/3" style="background-image: url('assets/img/cap/panama/list1/3.jpg');"></div>
                        <div class="discount-badge px-2 py-1 rounded text-xs font-bold" data-i18n="discount_badge_3">-15%</div>
                    </div>
                    <div class="p-4 flex-grow flex flex-col">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-lg font-semibold" data-i18n="product_3_title">Et Mestaci</h3>
                            <div class="price-tag px-2 py-1 rounded text-sm font-bold">$79.99</div>
                        </div>
                        <p class="text-gray-200 text-sm mb-3" data-i18n="product_3_desc">Maximum sun protection with style.</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_3_1">Gravidas</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_3_2">Dipisci Triste</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_3_3">Odio Aenean</span>
                        </div>
                        <div class="flex justify-between mt-auto pt-4">
                            <button class="btn-primary px-4 py-2 rounded-lg text-sm font-semibold flex items-center add-to-cart">
                                <i class="fas fa-shopping-cart mr-2"></i> <span data-i18n="add_to_cart">Add to Cart</span>
                            </button>
                            <button class="bg-black bg-opacity-50 hover:bg-opacity-70 text-white px-3 py-2 rounded-lg transition border border-gray-600">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Product 4 -->
                <div class="product-card animate__animated animate__fadeIn" data-id="4" data-name="Recusandae Culpa Tenetur" data-price="95.99" data-image="assets/img/cap/panama/list1/4.jpg">
                    <div class="relative overflow-hidden">
                        <div class="product-image" data-image-base="assets/img/cap/panama/list1/4" style="background-image: url('assets/img/cap/panama/list1/4.jpg');"></div>
                    </div>
                    <div class="p-4 flex-grow flex flex-col">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-lg font-semibold" data-i18n="product_4_title">Recusandae Culpa Tenetur</h3>
                            <div class="price-tag px-2 py-1 rounded text-sm font-bold">$95.99</div>
                        </div>
                        <p class="text-gray-200 text-sm mb-3" data-i18n="product_4_desc">Versatile hat for any occasion.</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_4_1">Elementum</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_4_2">Praesent Semper</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_4_3">Gravida</span>
                        </div>
                        <div class="flex justify-between mt-auto pt-4">
                            <button class="btn-primary px-4 py-2 rounded-lg text-sm font-semibold flex items-center add-to-cart">
                                <i class="fas fa-shopping-cart mr-2"></i> <span data-i18n="add_to_cart">Add to Cart</span>
                            </button>
                            <button class="bg-black bg-opacity-50 hover:bg-opacity-70 text-white px-3 py-2 rounded-lg transition border border-gray-600">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Product 5 -->
                <div class="product-card animate__animated animate__fadeIn" data-id="5" data-name="Minima os Soluta" data-price="119.99" data-image="assets/img/cap/panama/list1/5.jpg">
                    <div class="relative overflow-hidden">
                        <div class="product-image" data-image-base="assets/img/cap/panama/list1/5" style="background-image: url('assets/img/cap/panama/list1/5.jpg');"></div>
                    </div>
                    <div class="p-4 flex-grow flex flex-col">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-lg font-semibold" data-i18n="product_5_title">Minima os Soluta</h3>
                            <div class="price-tag px-2 py-1 rounded text-sm font-bold">$119.99</div>
                        </div>
                        <p class="text-gray-200 text-sm mb-3" data-i18n="product_5_desc">Classic nautical style with modern comfort.</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_5_1">Odio Aenean</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_5_2">Libero</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_5_3">Systi on Peras</span>
                        </div>
                        <div class="flex justify-between mt-auto pt-4">
                            <button class="btn-primary px-4 py-2 rounded-lg text-sm font-semibold flex items-center add-to-cart">
                                <i class="fas fa-shopping-cart mr-2"></i> <span data-i18n="add_to_cart">Add to Cart</span>
                            </button>
                            <button class="bg-black bg-opacity-50 hover:bg-opacity-70 text-white px-3 py-2 rounded-lg transition border border-gray-600">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Product 6 -->
                <div class="product-card animate__animated animate__fadeIn" data-id="6" data-name="Atque Facilis Maleti" data-price="249.99" data-image="assets/img/cap/panama/list1/6.jpg">
                    <div class="relative overflow-hidden">
                        <div class="product-image" data-image-base="assets/img/cap/panama/list1/6" style="background-image: url('assets/img/cap/panama/list1/6.jpg');"></div>
                        <div class="discount-badge px-2 py-1 rounded text-xs font-bold" data-i18n="discount_badge_6">-25%</div>
                    </div>
                    <div class="p-4 flex-grow flex flex-col">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-lg font-semibold" data-i18n="product_6_title">Atque Facilis Maleti</h3>
                            <div class="price-tag px-2 py-1 rounded text-sm font-bold">$249.99</div>
                        </div>
                        <p class="text-gray-200 text-sm mb-3" data-i18n="product_6_desc">The finest handwoven Panama hat available.</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_6_1">Lista e Fortna</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_6_2">Libero</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_6_3">Turpis</span>
                        </div>
                        <div class="flex justify-between mt-auto pt-4">
                            <button class="btn-primary px-4 py-2 rounded-lg text-sm font-semibold flex items-center add-to-cart">
                                <i class="fas fa-shopping-cart mr-2"></i> <span data-i18n="add_to_cart">Add to Cart</span>
                            </button>
                            <button class="bg-black bg-opacity-50 hover:bg-opacity-70 text-white px-3 py-2 rounded-lg transition border border-gray-600">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Product 7 -->
                <div class="product-card animate__animated animate__fadeIn" data-id="7" data-name="Illum ut Commodi" data-price="69.99" data-image="assets/img/cap/panama/list1/7.png">
                    <div class="relative overflow-hidden">
                        <div class="product-image" data-image-base="assets/img/cap/panama/list1/7" style="background-image: url('assets/img/cap/panama/list1/7.png');"></div>
                    </div>
                    <div class="p-4 flex-grow flex flex-col">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-lg font-semibold" data-i18n="product_7_title">Illum ut Commodi</h3>
                            <div class="price-tag px-2 py-1 rounded text-sm font-bold">$69.99</div>
                        </div>
                        <p class="text-gray-200 text-sm mb-3" data-i18n="product_7_desc">Everyday comfort with timeless style.</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_7_1">Quisus</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_7_2">Eris Culpa</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_7_3">Morbi Leo</span>
                        </div>
                        <div class="flex justify-between mt-auto pt-4">
                            <button class="btn-primary px-4 py-2 rounded-lg text-sm font-semibold flex items-center add-to-cart">
                                <i class="fas fa-shopping-cart mr-2"></i> <span data-i18n="add_to_cart">Add to Cart</span>
                            </button>
                            <button class="bg-black bg-opacity-50 hover:bg-opacity-70 text-white px-3 py-2 rounded-lg transition border border-gray-600">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Product 8 -->
                <div class="product-card animate__animated animate__fadeIn" data-id="8" data-name="Velit at Erum Vero" data-price="99.99" data-image="assets/img/cap/panama/list1/8.png">
                    <div class="relative overflow-hidden">
                        <div class="product-image" data-image-base="assets/img/cap/panama/list1/8" style="background-image: url('assets/img/cap/panama/list1/8.png');"></div>
                        <div class="discount-badge px-2 py-1 rounded text-xs font-bold" data-i18n="discount_badge_8">-30%</div>
                    </div>
                    <div class="p-4 flex-grow flex flex-col">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-lg font-semibold" data-i18n="product_8_title">Velit at Erum Vero</h3>
                            <div class="price-tag px-2 py-1 rounded text-sm font-bold">$99.99</div>
                        </div>
                        <p class="text-gray-200 text-sm mb-3" data-i18n="product_8_desc">Authentic design from Ecuador.</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_8_1">Arum Vero</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_8_2">Delect a Distinctio</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_8_3">Lista e Fortna</span>
                        </div>
                        <div class="flex justify-between mt-auto pt-4">
                            <button class="btn-primary px-4 py-2 rounded-lg text-sm font-semibold flex items-center add-to-cart">
                                <i class="fas fa-shopping-cart mr-2"></i> <span data-i18n="add_to_cart">Add to Cart</span>
                            </button>
                            <button class="bg-black bg-opacity-50 hover:bg-opacity-70 text-white px-3 py-2 rounded-lg transition border border-gray-600">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Product 9 -->
                <div class="product-card animate__animated animate__fadeIn" data-id="9" data-name="Nostrum Voluptatem" data-price="129.99" data-image="assets/img/cap/panama/list1/9.png">
                    <div class="relative overflow-hidden">
                        <div class="product-image" data-image-base="assets/img/cap/panama/list1/9" style="background-image: url('assets/img/cap/panama/list1/9.png');"></div>
                        <div class="discount-badge px-2 py-1 rounded text-xs font-bold" data-i18n="discount_badge_9">-10%</div>
                    </div>
                    <div class="p-4 flex-grow flex flex-col">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-lg font-semibold" data-i18n="product_9_title">Nostrum Voluptatem</h3>
                            <div class="price-tag px-2 py-1 rounded text-sm font-bold">$129.99</div>
                        </div>
                        <p class="text-gray-200 text-sm mb-3" data-i18n="product_9_desc">Elegant design for tropical adventures.</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_9_1">Voluptas</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_9_2">Consectetur</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_9_3">Adipiscing</span>
                        </div>
                        <div class="flex justify-between mt-auto pt-4">
                            <button class="btn-primary px-4 py-2 rounded-lg text-sm font-semibold flex items-center add-to-cart">
                                <i class="fas fa-shopping-cart mr-2"></i> <span data-i18n="add_to_cart">Add to Cart</span>
                            </button>
                            <button class="bg-black bg-opacity-50 hover:bg-opacity-70 text-white px-3 py-2 rounded-lg transition border border-gray-600">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Product 10 -->
                <div class="product-card animate__animated animate__fadeIn" data-id="10" data-name="Quasi Est Dolor" data-price="89.99" data-image="assets/img/cap/panama/list1/10.png">
                    <div class="relative overflow-hidden">
                        <div class="product-image" data-image-base="assets/img/cap/panama/list1/10" style="background-image: url('assets/img/cap/panama/list1/10.png');"></div>
                    </div>
                    <div class="p-4 flex-grow flex flex-col">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-lg font-semibold" data-i18n="product_10_title">Quasi Est Dolor</h3>
                            <div class="price-tag px-2 py-1 rounded text-sm font-bold">$89.99</div>
                        </div>
                        <p class="text-gray-200 text-sm mb-3" data-i18n="product_10_desc">Casual comfort for everyday wear.</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_10_1">Ipsum Dolor</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_10_2">Sit Amet</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_10_3">Elit Sed</span>
                        </div>
                        <div class="flex justify-between mt-auto pt-4">
                            <button class="btn-primary px-4 py-2 rounded-lg text-sm font-semibold flex items-center add-to-cart">
                                <i class="fas fa-shopping-cart mr-2"></i> <span data-i18n="add_to_cart">Add to Cart</span>
                            </button>
                            <button class="bg-black bg-opacity-50 hover:bg-opacity-70 text-white px-3 py-2 rounded-lg transition border border-gray-600">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Product 11 -->
                <div class="product-card animate__animated animate__fadeIn" data-id="11" data-name="Magnam Repudiandae" data-price="199.99" data-image="assets/img/cap/panama/list1/11.png">
                    <div class="relative overflow-hidden">
                        <div class="product-image" data-image-base="assets/img/cap/panama/list1/11" style="background-image: url('assets/img/cap/panama/list1/11.png');"></div>
                        <div class="discount-badge px-2 py-1 rounded text-xs font-bold" data-i18n="discount_badge_11">-20%</div>
                    </div>
                    <div class="p-4 flex-grow flex flex-col">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-lg font-semibold" data-i18n="product_11_title">Magnam Repudiandae</h3>
                            <div class="price-tag px-2 py-1 rounded text-sm font-bold">$199.99</div>
                        </div>
                        <p class="text-gray-200 text-sm mb-3" data-i18n="product_11_desc">Premium craftsmanship for luxury seekers.</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_11_1">Tempor Incididunt</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_11_2">Labore Dolore</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_11_3">Magna Aliqua</span>
                        </div>
                        <div class="flex justify-between mt-auto pt-4">
                            <button class="btn-primary px-4 py-2 rounded-lg text-sm font-semibold flex items-center add-to-cart">
                                <i class="fas fa-shopping-cart mr-2"></i> <span data-i18n="add_to_cart">Add to Cart</span>
                            </button>
                            <button class="bg-black bg-opacity-50 hover:bg-opacity-70 text-white px-3 py-2 rounded-lg transition border border-gray-600">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Product 12 -->
                <div class="product-card animate__animated animate__fadeIn" data-id="12" data-name="Aliquid Architecto" data-price="149.99" data-image="assets/img/cap/panama/list1/12.jpeg">
                    <div class="relative overflow-hidden">
                        <div class="product-image" data-image-base="assets/img/cap/panama/list1/12" style="background-image: url('assets/img/cap/panama/list1/12.jpeg');"></div>
                    </div>
                    <div class="p-4 flex-grow flex flex-col">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-lg font-semibold" data-i18n="product_12_title">Aliquid Architecto</h3>
                            <div class="price-tag px-2 py-1 rounded text-sm font-bold">$149.99</div>
                        </div>
                        <p class="text-gray-200 text-sm mb-3" data-i18n="product_12_desc">Bold style for modern explorers.</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_12_1">Ut Enim</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_12_2">Minim Veniam</span>
                            <span class="tag text-xs px-2 py-1 rounded" data-i18n="tag_12_3">Nostrud Exercitation</span>
                        </div>
                        <div class="flex justify-between mt-auto pt-4">
                            <button class="btn-primary px-4 py-2 rounded-lg text-sm font-semibold flex items-center add-to-cart">
                                <i class="fas fa-shopping-cart mr-2"></i> <span data-i18n="add_to_cart">Add to Cart</span>
                            </button>
                            <button class="bg-black bg-opacity-50 hover:bg-opacity-70 text-white px-3 py-2 rounded-lg transition border border-gray-600">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 移除加载更多按钮 -->
        </div>
    </section>

    <!-- Cart Sidebar -->
    <div id="cartOverlay" class="cart-overlay fixed inset-0 bg-black bg-opacity-50 z-40"></div>

    <div id="cartSidebar" class="cart-sidebar fixed top-0 right-0 w-full sm:w-96 h-full bg-white shadow-lg z-50 overflow-y-auto">
        <div class="p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold" data-i18n="your_cart">Your Cart</h2>
                <button id="closeCart" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div id="cartItems" class="mb-6">
                <!-- Cart items will be added here dynamically -->
                <div class="text-center py-8 text-gray-500">
                    <i class="fas fa-shopping-cart text-4xl mb-2"></i>
                    <p data-i18n="cart_empty">Your cart is empty</p>
                </div>
            </div>

            <div class="border-t border-gray-200 pt-4">
                <div class="flex justify-between mb-2">
                    <span class="font-semibold" data-i18n="subtotal">Subtotal:</span>
                    <span id="cartSubtotal" class="font-semibold">$0.00</span>
                </div>
                <div class="flex justify-between mb-4">
                    <span data-i18n="shipping">Shipping:</span>
                    <span data-i18n="free">Free</span>
                </div>
                <div class="flex justify-between text-lg font-bold mb-6">
                    <span data-i18n="total">Total:</span>
                    <span id="cartTotal" class="text-green-600">$0.00</span>
                </div>

                <a href="pay.html" id="checkoutBtn" class="w-full bg-green-600 hover:bg-green-700 text-white py-3 rounded-lg font-semibold transition disabled:opacity-50 block text-center">
                    <span data-i18n="proceed_to_checkout">Proceed to Checkout</span>
                </a>
            </div>
        </div>
    </div>

    <!-- 产品详情悬浮图框 -->
    <div id="productModal" class="modal-overlay">
        <div class="product-modal">
            <button id="modalClose" class="modal-close">
                <i class="fas fa-times"></i>
            </button>
            <div class="modal-image-container">
                <img id="modalImage" class="modal-image" src="" alt="Product Image">
            </div>
            <div class="modal-content">
                <h2 id="modalTitle" class="modal-title"></h2>
                <div id="modalPrice" class="modal-price"></div>
                <p id="modalDescription" class="modal-description"></p>
                <div id="modalTags" class="modal-tags">
                    <!-- 标签将通过JavaScript动态添加 -->
                </div>
                <button id="modalAddToCart" class="modal-add-to-cart">
                    <i class="fas fa-shopping-cart mr-2"></i> <span data-i18n="add_to_cart">加入购物车</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Updated Footer with Black Banner -->
    <footer style="position: fixed; bottom: 0; left: 0; right: 0; background-color: #000000; padding: 10px 0; text-align: center; color: #ffffff;">
        <p data-i18n="footer_copyright_text">2023 Panama Hats Collection. All rights reserved.</p>
    </footer>

    <script type="text/javascript">
        var gk_isXlsx = false;
        var gk_xlsxFileLookup = {};
        var gk_fileData = {};
        function filledCell(cell) {
            return cell !== '' && cell != null;
        }
        function loadFileData(filename) {
            if (gk_isXlsx && gk_xlsxFileLookup[filename]) {
                try {
                    var workbook = XLSX.read(gk_fileData[filename], { type: 'base64' });
                    var firstSheetName = workbook.SheetNames[0];
                    var worksheet = workbook.Sheets[firstSheetName];

                    // Convert sheet to JSON to filter blank rows
                    var jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, blankrows: false, defval: '' });
                    // Filter out blank rows (rows where all cells are empty, null, or undefined)
                    var filteredData = jsonData.filter(row => row.some(filledCell));

                    // Heuristic to find the header row by ignoring rows with fewer filled cells than the next row
                    var headerRowIndex = filteredData.findIndex((row, index) =>
                        row.filter(filledCell).length >= filteredData[index + 1]?.filter(filledCell).length
                    );
                    // Fallback
                    if (headerRowIndex === -1 || headerRowIndex > 25) {
                        headerRowIndex = 0;
                    }

                    // Convert filtered JSON back to CSV
                    var csv = XLSX.utils.aoa_to_sheet(filteredData.slice(headerRowIndex)); // Create a new sheet from filtered array of arrays
                    csv = XLSX.utils.sheet_to_csv(csv, { header: 1 });
                    return csv;
                } catch (e) {
                    console.error(e);
                    return "";
                }
            }
            return gk_fileData[filename] || "";
        }
    </script>

    <script>
        // Cart functionality
        let cart = [];
        const cartButton = document.getElementById('cartButton');
        const cartCount = document.getElementById('cartCount');
        const cartSidebar = document.getElementById('cartSidebar');
        const cartOverlay = document.getElementById('cartOverlay');
        const closeCart = document.getElementById('closeCart');
        const cartItems = document.getElementById('cartItems');
        const cartSubtotal = document.getElementById('cartSubtotal');
        const cartTotal = document.getElementById('cartTotal');
        const checkoutBtn = document.getElementById('checkoutBtn');

        // 产品详情悬浮图框元素
        const productModal = document.getElementById('productModal');
        const modalClose = document.getElementById('modalClose');
        const modalImage = document.getElementById('modalImage');
        const modalTitle = document.getElementById('modalTitle');
        const modalPrice = document.getElementById('modalPrice');
        const modalDescription = document.getElementById('modalDescription');
        const modalTags = document.getElementById('modalTags');
        const modalAddToCart = document.getElementById('modalAddToCart');

        // 从localStorage加载购物车数据
        function loadCart() {
            const savedCart = localStorage.getItem('panamaCap_cart');
            if (savedCart) {
                try {
                    cart = JSON.parse(savedCart);
                    console.log('从localStorage加载购物车数据:', cart.length, '个商品');
                    updateCart();
                } catch (e) {
                    console.error('解析购物车数据出错:', e);
                    cart = [];
                }
            }
        }

        // 保存购物车数据到localStorage
        function saveCart() {
            localStorage.setItem('panamaCap_cart', JSON.stringify(cart));
            console.log('购物车数据已保存，共', cart.length, '个商品');
        }

        // Toggle cart visibility
        cartButton.addEventListener('click', () => {
            // 确保购物车数据是最新的
            loadCart();

            // 检测并设置所有产品图片
            if (typeof window.initializeProductImages === 'function') {
                window.initializeProductImages();
            } else {
                console.error('图片检测功能未加载');
            }
            // 打开购物车侧边栏
            cartSidebar.classList.add('open');
            cartOverlay.classList.add('open');
        });

        closeCart.addEventListener('click', () => {
            cartSidebar.classList.remove('open');
            cartOverlay.classList.remove('open');
        });

        cartOverlay.addEventListener('click', () => {
            cartSidebar.classList.remove('open');
            cartOverlay.classList.remove('open');
        });

        let currentProduct=null;
        document.querySelectorAll('.product-image').forEach(img=>{
            img.addEventListener('click',async function(){
                const card=this.closest('.product-card');
                const id=card.dataset.id;
                const name=card.dataset.name;
                const price=card.dataset.price;
                const image=card.dataset.image;
                const desc=card.querySelector('[data-i18n^="product_"][data-i18n$="_desc"]');
                const tags=card.querySelectorAll('.tag');

                // 获取图片的基础路径（去掉扩展名）
                const basePath = image.replace(/\.[^/.]+$/, "");

                // 显示模态框
                productModal.classList.add('open');
                document.body.style.overflow = 'hidden'; // 防止背景滚动

                // 重置模态框图片状态
                modalImage.style.opacity = '0';
                modalImage.style.transition = 'opacity 1s ease-in-out';

                // 使用新的视频+图片检测功能
                if (typeof window.detectAndPlayModalMedia === 'function') {
                    try {
                        console.log('开始检测产品媒体文件:', basePath);
                        await window.detectAndPlayModalMedia(basePath, modalImage);

                        // 获取最终的图片URL用于购物车
                        const finalImageUrl = modalImage.src;
                        currentProduct = {id, name, price, image: finalImageUrl};
                    } catch (error) {
                        console.error('模态框媒体检测出错:', error);
                        // 回退到原始图片检测
                        const detectedImageUrl = await window.detectModalImage(basePath);
                        modalImage.src = detectedImageUrl;
                        modalImage.style.opacity = '1';
                        currentProduct = {id, name, price, image: detectedImageUrl};
                    }
                } else {
                    console.warn('视频+图片检测功能未加载，使用原始图片检测');
                    const detectedImageUrl = await window.detectModalImage(basePath);
                    modalImage.src = detectedImageUrl;
                    modalImage.style.opacity = '1';
                    currentProduct = {id, name, price, image: detectedImageUrl};
                }
                modalTitle.textContent=name;
                modalPrice.textContent='$'+price;
                if(desc)modalDescription.textContent=desc.textContent;

                modalTags.innerHTML='';
                tags.forEach(tag=>{
                    const t=document.createElement('span');
                    t.className='modal-tag';
                    t.textContent=tag.textContent;
                    modalTags.appendChild(t);
                });
            });
        });

        modalClose.addEventListener('click',()=>{
            productModal.classList.remove('open');
            document.body.style.overflow='';
            currentProduct=null;
        });

        productModal.addEventListener('click',e=>{
            if(e.target===productModal){
                productModal.classList.remove('open');
                document.body.style.overflow='';
                currentProduct=null;
            }
        });

        modalAddToCart.addEventListener('click',()=>{
            if(currentProduct){
                const item=cart.find(i=>i.id===currentProduct.id);
                if(item)item.quantity+=1;
                else cart.push({
                    id:currentProduct.id,
                    name:currentProduct.name,
                    price:parseFloat(currentProduct.price),
                    image:currentProduct.image,
                    quantity:1
                });

                updateCart();
                const txt=modalAddToCart.innerHTML;
                modalAddToCart.innerHTML='<i class="fas fa-check mr-2"></i> 已添加到购物车!';
                modalAddToCart.style.backgroundColor='#28a745';
                setTimeout(()=>{
                    modalAddToCart.innerHTML=txt;
                    modalAddToCart.style.backgroundColor='';
                },1500);
            }
        });

        document.querySelectorAll('.add-to-cart').forEach(btn=>{
            btn.addEventListener('click',function(){
                const card=this.closest('.product-card');
                const id=card.dataset.id;
                const name=card.dataset.name;
                const price=parseFloat(card.dataset.price);
                const image=card.dataset.image;

                const item=cart.find(i=>i.id===id);
                if(item)item.quantity+=1;
                else cart.push({id,name,price,image,quantity:1});

                updateCart();
                cartSidebar.classList.add('open');
                cartOverlay.classList.add('open');

                const added=document.createElement('span');
                added.textContent='Added!';
                added.className='text-green-600 ml-2';
                this.appendChild(added);
                setTimeout(()=>added.remove(),1000);
            });
        });

        // Update cart display
        function updateCart() {
            // Update cart count
            const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
            cartCount.textContent = totalItems;

            // Update cart items list
            if (cart.length === 0) {
                // 获取当前语言
                const currentLang = localStorage.getItem('selectedLanguage') || 'zh';
                // 获取翻译数据
                const translationData = window.translations || window.languageData || {};
                // 获取翻译文本
                let emptyCartText = 'Your cart is empty';
                if (translationData[currentLang] && translationData[currentLang]['cart_empty']) {
                    emptyCartText = translationData[currentLang]['cart_empty'];
                }

                cartItems.innerHTML = `
                    <div class="text-center py-8 text-gray-500">
                        <i class="fas fa-shopping-cart text-4xl mb-2"></i>
                        <p data-i18n="cart_empty">${emptyCartText}</p>
                    </div>
                `;
                checkoutBtn.classList.add('opacity-50');
                checkoutBtn.style.pointerEvents = 'none';
            } else {
                cartItems.innerHTML = '';
                cart.forEach(item => {
                    const itemElement = document.createElement('div');
                    itemElement.className = 'flex items-center py-4 border-b border-gray-200';
                    itemElement.innerHTML = `
                        <div class="w-20 h-20 bg-gray-100 rounded-lg overflow-hidden mr-4">
                            <img src="${item.image}" alt="${item.name}" class="w-full h-full object-cover">
                        </div>
                        <div class="flex-1">
                            <h3 class="font-semibold">${item.name}</h3>
                            <p class="text-gray-600">$${item.price.toFixed(2)}</p>
                            <div class="quantity-selector mt-2">
                                <button class="quantity-btn decrease" data-id="${item.id}">-</button>
                                <input type="number" min="1" value="${item.quantity}" class="quantity-input" data-id="${item.id}">
                                <button class="quantity-btn increase" data-id="${item.id}">+</button>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="font-semibold">$${(item.price * item.quantity).toFixed(2)}</p>
                            <button class="text-red-500 hover:text-red-700 remove-item mt-1" data-id="${item.id}">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    `;
                    cartItems.appendChild(itemElement);
                });

                checkoutBtn.classList.remove('opacity-50');
                checkoutBtn.style.pointerEvents = 'auto';
            }

            // Update totals
            const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            cartSubtotal.textContent = `$${subtotal.toFixed(2)}`;
            cartTotal.textContent = `$${subtotal.toFixed(2)}`;

            // Add event listeners to quantity controls
            document.querySelectorAll('.decrease').forEach(button => {
                button.addEventListener('click', function() {
                    const itemId = this.dataset.id;
                    const item = cart.find(item => item.id === itemId);

                    if (item.quantity > 1) {
                        item.quantity -= 1;
                        updateCart();
                    }
                });
            });

            document.querySelectorAll('.increase').forEach(button => {
                button.addEventListener('click', function() {
                    const itemId = this.dataset.id;
                    const item = cart.find(item => item.id === itemId);
                    item.quantity += 1;
                    updateCart();
                });
            });

            document.querySelectorAll('.quantity-input').forEach(input => {
                input.addEventListener('change', function() {
                    const itemId = this.dataset.id;
                    const item = cart.find(item => item.id === itemId);
                    const newQuantity = parseInt(this.value);

                    if (newQuantity >= 1) {
                        item.quantity = newQuantity;
                        updateCart();
                    } else {
                        this.value = item.quantity;
                    }
                });
            });

            document.querySelectorAll('.remove-item').forEach(button => {
                button.addEventListener('click', function() {
                    const itemId = this.dataset.id;
                    const itemIndex = cart.findIndex(item => item.id === itemId);

                    if (itemIndex !== -1) {
                        cart.splice(itemIndex, 1);
                        updateCart();
                    }
                });
            });

            // 保存购物车数据到localStorage
            saveCart();
        }

        // Checkout button
        checkoutBtn.addEventListener('click', (e) => {
            if (cart.length === 0) {
                e.preventDefault();
                // 获取当前语言
                const currentLang = localStorage.getItem('selectedLanguage') || 'zh';
                // 根据语言显示不同的提示
                const messages = {
                    'zh': '购物车为空，请先添加商品',
                    'en': 'Your cart is empty, please add items first',
                    'es': 'Su carrito está vacío, por favor agregue artículos primero',
                    'fr': 'Votre panier est vide, veuillez d\'abord ajouter des articles',
                    'de': 'Ihr Warenkorb ist leer, bitte fügen Sie zuerst Artikel hinzu',
                    'ja': 'カートが空です。最初に商品を追加してください',
                    'ko': '장바구니가 비어 있습니다. 먼저 항목을 추가하십시오',
                    'ar': 'سلة التسوق فارغة، يرجى إضافة العناصر أولاً'
                };
                alert(messages[currentLang] || messages['en']);
                return;
            }

            // 购物车数据已经在updateCart函数中保存到localStorage
            console.log('正在前往结账页面，购物车商品数量:', cart.length);
        });

        // Simple load more functionality
        document.getElementById('loadMore').addEventListener('click', function() {
            const spinner = document.getElementById('spinner');
            const button = this;

            // Show spinner
            spinner.classList.remove('hidden');
            button.disabled = true;

            // Simulate loading
            setTimeout(function() {
                // Hide spinner
                spinner.classList.add('hidden');
                button.disabled = false;

                // In a real app, you would load more products here
                alert('More products would be loaded in a real application');
            }, 1500);
        });
    </script>
    <script>
        // Cart functionality
        let cart = [];
        const cartButton = document.getElementById('cartButton');
        const cartCount = document.getElementById('cartCount');
        const cartSidebar = document.getElementById('cartSidebar');
        const cartOverlay = document.getElementById('cartOverlay');
        const closeCart = document.getElementById('closeCart');
        const cartItems = document.getElementById('cartItems');
        const cartSubtotal = document.getElementById('cartSubtotal');
        const cartTotal = document.getElementById('cartTotal');
        const checkoutBtn = document.getElementById('checkoutBtn');

        // 产品详情悬浮图框元素
        const productModal = document.getElementById('productModal');
        const modalClose = document.getElementById('modalClose');
        const modalImage = document.getElementById('modalImage');
        const modalTitle = document.getElementById('modalTitle');
        const modalPrice = document.getElementById('modalPrice');
        const modalDescription = document.getElementById('modalDescription');
        const modalTags = document.getElementById('modalTags');
        const modalAddToCart = document.getElementById('modalAddToCart');

        // 从localStorage加载购物车数据
        function loadCart() {
            const savedCart = localStorage.getItem('panamaCap_cart');
            if (savedCart) {
                try {
                    cart = JSON.parse(savedCart);
                    console.log('从localStorage加载购物车数据:', cart.length, '个商品');
                    updateCart();
                } catch (e) {
                    console.error('解析购物车数据出错:', e);
                    cart = [];
                }
            }
        }

        // 保存购物车数据到localStorage
        function saveCart() {
            localStorage.setItem('panamaCap_cart', JSON.stringify(cart));
            console.log('购物车数据已保存，共', cart.length, '个商品');
        }

        // Toggle cart visibility
        cartButton.addEventListener('click', () => {
            // 确保购物车数据是最新的
            loadCart();
            // 打开购物车侧边栏
            cartSidebar.classList.add('open');
            cartOverlay.classList.add('open');
        });

        closeCart.addEventListener('click', () => {
            cartSidebar.classList.remove('open');
            cartOverlay.classList.remove('open');
        });

        cartOverlay.addEventListener('click', () => {
            cartSidebar.classList.remove('open');
            cartOverlay.classList.remove('open');
        });

        // 产品图片点击事件 - 显示模态框
        let currentProduct = null;
        document.querySelectorAll('.product-image').forEach(img => {
            img.addEventListener('click', async function() {
                const card = this.closest('.product-card');
                const id = card.dataset.id;
                const name = card.dataset.name;
                const price = card.dataset.price;
                const image = card.dataset.image;
                const desc = card.querySelector('[data-i18n^="product_"][data-i18n$="_desc"]');
                const tags = card.querySelectorAll('.tag');

                // 获取图片的基础路径（去掉扩展名）
                const basePath = image.replace(/\.[^/.]+$/, "");

                // 显示模态框
                productModal.classList.add('open');
                document.body.style.overflow = 'hidden'; // 防止背景滚动

                // 重置模态框图片状态
                modalImage.style.opacity = '0';
                modalImage.style.transition = 'opacity 1s ease-in-out';

                // 使用新的视频+图片检测功能
                if (typeof window.detectAndPlayModalMedia === 'function') {
                    try {
                        console.log('开始检测产品媒体文件:', basePath);
                        await window.detectAndPlayModalMedia(basePath, modalImage);

                        // 获取最终的图片URL用于购物车
                        const finalImageUrl = modalImage.src;
                        currentProduct = {id, name, price, image: finalImageUrl};
                    } catch (error) {
                        console.error('模态框媒体检测出错:', error);
                        // 回退到原始图片检测
                        const detectedImageUrl = await window.detectModalImage(basePath);
                        modalImage.src = detectedImageUrl;
                        modalImage.style.opacity = '1';
                        currentProduct = {id, name, price, image: detectedImageUrl};
                    }
                } else {
                    console.warn('视频+图片检测功能未加载，使用原始图片检测');
                    const detectedImageUrl = await window.detectModalImage(basePath);
                    modalImage.src = detectedImageUrl;
                    modalImage.style.opacity = '1';
                    currentProduct = {id, name, price, image: detectedImageUrl};
                }
                modalTitle.textContent = name;
                modalPrice.textContent = '$' + price;
                if (desc) modalDescription.textContent = desc.textContent;

                modalTags.innerHTML = '';
                tags.forEach(tag => {
                    const t = document.createElement('span');
                    t.className = 'modal-tag';
                    t.textContent = tag.textContent;
                    modalTags.appendChild(t);
                });
            });
        });

        // 模态框关闭事件
        modalClose.addEventListener('click', () => {
            productModal.classList.remove('open');
            document.body.style.overflow = '';
            currentProduct = null;
        });

        productModal.addEventListener('click', e => {
            if (e.target === productModal) {
                productModal.classList.remove('open');
                document.body.style.overflow = '';
                currentProduct = null;
            }
        });

        // 模态框中的添加到购物车按钮
        modalAddToCart.addEventListener('click', () => {
            if (currentProduct) {
                const item = cart.find(i => i.id === currentProduct.id);
                if (item) item.quantity += 1;
                else cart.push({
                    id: currentProduct.id,
                    name: currentProduct.name,
                    price: parseFloat(currentProduct.price),
                    image: currentProduct.image,
                    quantity: 1
                });

                updateCart();
                const txt = modalAddToCart.innerHTML;
                modalAddToCart.innerHTML = '<i class="fas fa-check mr-2"></i> 已添加到购物车!';
                modalAddToCart.style.backgroundColor = '#28a745';
                setTimeout(() => {
                    modalAddToCart.innerHTML = txt;
                    modalAddToCart.style.backgroundColor = '';
                }, 1500);
            }
        });

        // 产品卡片中的添加到购物车按钮
        document.querySelectorAll('.add-to-cart').forEach(btn => {
            btn.addEventListener('click', function() {
                const card = this.closest('.product-card');
                const id = card.dataset.id;
                const name = card.dataset.name;
                const price = parseFloat(card.dataset.price);
                const image = card.dataset.image;

                const item = cart.find(i => i.id === id);
                if (item) item.quantity += 1;
                else cart.push({id, name, price, image, quantity: 1});

                updateCart();
                cartSidebar.classList.add('open');
                cartOverlay.classList.add('open');

                const added = document.createElement('span');
                added.textContent = 'Added!';
                added.className = 'text-green-600 ml-2';
                this.appendChild(added);
                setTimeout(() => added.remove(), 1000);
            });
        });

        // Update cart display
        function updateCart() {
            // Update cart count
            const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
            cartCount.textContent = totalItems;

            // Update cart items list
            if (cart.length === 0) {
                // 获取当前语言
                const currentLang = localStorage.getItem('selectedLanguage') || 'zh';
                // 获取翻译数据
                const translationData = window.translations || window.languageData || {};
                // 获取翻译文本
                let emptyCartText = 'Your cart is empty';
                if (translationData[currentLang] && translationData[currentLang]['cart_empty']) {
                    emptyCartText = translationData[currentLang]['cart_empty'];
                }

                cartItems.innerHTML = `
                    <div class="text-center py-8 text-gray-500">
                        <i class="fas fa-shopping-cart text-4xl mb-2"></i>
                        <p data-i18n="cart_empty">${emptyCartText}</p>
                    </div>
                `;
                checkoutBtn.classList.add('opacity-50');
                checkoutBtn.style.pointerEvents = 'none';
            } else {
                cartItems.innerHTML = '';
                cart.forEach(item => {
                    const itemElement = document.createElement('div');
                    itemElement.className = 'flex items-center py-4 border-b border-gray-200';
                    itemElement.innerHTML = `
                        <div class="w-20 h-20 bg-gray-100 rounded-lg overflow-hidden mr-4">
                            <img src="${item.image}" alt="${item.name}" class="w-full h-full object-cover">
                        </div>
                        <div class="flex-1">
                            <h3 class="font-semibold">${item.name}</h3>
                            <p class="text-gray-600">$${item.price.toFixed(2)}</p>
                            <div class="quantity-selector mt-2">
                                <button class="quantity-btn decrease" data-id="${item.id}">-</button>
                                <input type="number" min="1" value="${item.quantity}" class="quantity-input" data-id="${item.id}">
                                <button class="quantity-btn increase" data-id="${item.id}">+</button>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="font-semibold">$${(item.price * item.quantity).toFixed(2)}</p>
                            <button class="text-red-500 hover:text-red-700 remove-item mt-1" data-id="${item.id}">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    `;
                    cartItems.appendChild(itemElement);
                });

                checkoutBtn.classList.remove('opacity-50');
                checkoutBtn.style.pointerEvents = 'auto';
            }

            // Update totals
            const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            cartSubtotal.textContent = `$${subtotal.toFixed(2)}`;
            cartTotal.textContent = `$${subtotal.toFixed(2)}`;

            // Add event listeners to quantity controls
            document.querySelectorAll('.decrease').forEach(button => {
                button.addEventListener('click', function() {
                    const itemId = this.dataset.id;
                    const item = cart.find(item => item.id === itemId);

                    if (item.quantity > 1) {
                        item.quantity -= 1;
                        updateCart();
                    }
                });
            });

            document.querySelectorAll('.increase').forEach(button => {
                button.addEventListener('click', function() {
                    const itemId = this.dataset.id;
                    const item = cart.find(item => item.id === itemId);
                    item.quantity += 1;
                    updateCart();
                });
            });

            document.querySelectorAll('.quantity-input').forEach(input => {
                input.addEventListener('change', function() {
                    const itemId = this.dataset.id;
                    const item = cart.find(item => item.id === itemId);
                    const newQuantity = parseInt(this.value);

                    if (newQuantity >= 1) {
                        item.quantity = newQuantity;
                        updateCart();
                    } else {
                        this.value = item.quantity;
                    }
                });
            });

            document.querySelectorAll('.remove-item').forEach(button => {
                button.addEventListener('click', function() {
                    const itemId = this.dataset.id;
                    const itemIndex = cart.findIndex(item => item.id === itemId);

                    if (itemIndex !== -1) {
                        cart.splice(itemIndex, 1);
                        updateCart();
                    }
                });
            });

            // 保存购物车数据到localStorage
            saveCart();
        }

        // Checkout button
        checkoutBtn.addEventListener('click', (e) => {
            if (cart.length === 0) {
                e.preventDefault();
                // 获取当前语言
                const currentLang = localStorage.getItem('selectedLanguage') || 'zh';
                // 根据语言显示不同的提示
                const messages = {
                    'zh': '购物车为空，请先添加商品',
                    'en': 'Your cart is empty, please add items first',
                    'es': 'Su carrito está vacío, por favor agregue artículos primero',
                    'fr': 'Votre panier est vide, veuillez d\'abord ajouter des articles',
                    'de': 'Ihr Warenkorb ist leer, bitte fügen Sie zuerst Artikel hinzu',
                    'ja': 'カートが空です。最初に商品を追加してください',
                    'ko': '장바구니가 비어 있습니다. 먼저 항목을 추가하십시오',
                    'ar': 'سلة التسوق فارغة، يرجى إضافة العناصر أولاً'
                };
                alert(messages[currentLang] || messages['en']);
                return;
            }

            // 购物车数据已经在updateCart函数中保存到localStorage
            console.log('正在前往结账页面，购物车商品数量:', cart.length);
        });

        // 全局变量用于跟踪懒加载清理函数
        let lazyLoadCleanup = null;

        // 加载更多按钮点击事件
        document.getElementById('loadMore').addEventListener('click', async function() {
            try {
                console.log('用户点击加载更多按钮');
                const result = await window.loadMoreProducts('assets/img/cap/panama/list1');
                console.log('加载更多产品结果:', result);

                // 成功提示已在loadMoreProducts函数中处理
            } catch (error) {
                console.error('加载更多产品失败:', error);
                // 错误提示已在loadMoreProducts函数中处理
            }
        });

        // 监听自定义事件来处理新产品的模态框显示
        document.addEventListener('showProductModal', async function(event) {
            const { id, name, price, image, description, tags, basePath, subImages } = event.detail;

            // 保存当前产品信息
            currentProduct = { id, name, price, image };

            // 设置模态框内容
            modalImage.src = image;
            modalTitle.textContent = name;
            modalPrice.textContent = '$' + price;
            modalDescription.textContent = description;

            // 清空并添加标签
            modalTags.innerHTML = '';
            tags.forEach(tagText => {
                const tag = document.createElement('span');
                tag.className = 'modal-tag';
                tag.textContent = tagText;
                modalTags.appendChild(tag);
            });

            // 如果有子图片，创建缩略图
            if (subImages && subImages.length > 0) {
                console.log(`为新产品创建 ${subImages.length} 个缩略图`);
                const modalContainer = productModal.querySelector('.product-modal') || productModal;
                if (typeof window.createSubImageThumbnails === 'function') {
                    window.createSubImageThumbnails(subImages, modalImage, modalContainer);
                }
            }

            // 显示模态框
            productModal.classList.add('open');
            document.body.style.overflow = 'hidden';
        });

        // 监听自定义事件来处理添加到购物车
        document.addEventListener('addToCart', function(event) {
            const { id, name, price, image, quantity } = event.detail;

            // 检查购物车中是否已有该商品
            const existingItem = cart.find(item => item.id === id);
            if (existingItem) {
                existingItem.quantity += quantity;
            } else {
                cart.push({ id, name, price, image, quantity });
            }

            // 更新购物车UI并保存
            updateCart();
            saveCart();

            console.log('商品已添加到购物车:', { id, name, price, quantity });
        });

        // 页面加载完成后初始化i18next
        document.addEventListener('DOMContentLoaded', function() {
            console.log('PanamaCap-list1.html: DOM加载完成，准备初始化');

            // 添加data-i18n属性到需要翻译的元素
            const loadMoreButton = document.getElementById('loadMore');
            if (loadMoreButton) {
                const loadMoreText = loadMoreButton.querySelector('span');
                if (loadMoreText) {
                    loadMoreText.setAttribute('data-i18n', 'load_more');
                    console.log('已添加data-i18n属性到Load More按钮');
                }
            }

            // 添加data-i18n属性到Your Cart标题
            const cartTitle = document.querySelector('.cart-sidebar h2');
            if (cartTitle) {
                cartTitle.setAttribute('data-i18n', 'your_cart');
                console.log('已添加data-i18n属性到Your Cart标题');
            }

            // 添加data-i18n属性到Subtotal, Shipping, Total
            const subtotalLabel = document.querySelector('.cart-summary div:nth-child(1) div:first-child');
            if (subtotalLabel) {
                subtotalLabel.setAttribute('data-i18n', 'subtotal');
                console.log('已添加data-i18n属性到Subtotal标签');
            }

            const shippingLabel = document.querySelector('.cart-summary div:nth-child(2) div:first-child');
            if (shippingLabel) {
                shippingLabel.setAttribute('data-i18n', 'shipping');
                console.log('已添加data-i18n属性到Shipping标签');
            }

            const freeText = document.querySelector('.cart-summary div:nth-child(2) div:last-child');
            if (freeText) {
                freeText.setAttribute('data-i18n', 'free');
                console.log('已添加data-i18n属性到Free文本');
            }

            const totalLabel = document.querySelector('.cart-summary div:nth-child(3) div:first-child');
            if (totalLabel) {
                totalLabel.setAttribute('data-i18n', 'total');
                console.log('已添加data-i18n属性到Total标签');
            }

            // 添加data-i18n属性到Proceed to Checkout按钮
            const checkoutButton = document.querySelector('.cart-sidebar a.bg-green-600');
            if (checkoutButton) {
                checkoutButton.setAttribute('data-i18n', 'proceed_to_checkout');
                console.log('已添加data-i18n属性到Proceed to Checkout按钮');
            }

            // 确保所有"Add to Cart"按钮都有正确的data-i18n属性
            document.querySelectorAll('.add-to-cart span').forEach(span => {
                // 检查当前属性
                const currentAttr = span.getAttribute('data-i18n');
                console.log('按钮属性检查:', span.textContent, 'data-i18n=', currentAttr);

                // 如果是btn_latest或者没有属性，设置为add_to_cart
                if (currentAttr === 'btn_latest' || !currentAttr) {
                    span.setAttribute('data-i18n', 'add_to_cart');
                    console.log('修正按钮属性为add_to_cart');
                }
            });

            // 获取当前语言
            const savedLanguage = localStorage.getItem('selectedLanguage') || 'zh';
            console.log('当前语言设置:', savedLanguage);

            // 设置HTML语言属性
            document.documentElement.setAttribute('lang', savedLanguage);
            console.log('设置HTML lang属性为:', savedLanguage);

            // 设置RTL支持
            if (savedLanguage === 'ar') {
                document.documentElement.dir = 'rtl';
                document.body.classList.add('rtl');
            } else {
                document.documentElement.dir = 'ltr';
                document.body.classList.remove('rtl');
            }

            // 定义一个函数来应用所有标签的翻译
            function applyTagTranslations() {
                console.log('应用所有标签的翻译');
                const currentLang = localStorage.getItem('selectedLanguage') || 'zh';
                const translationData = window.translations || {};

                // 如果找不到当前语言的翻译数据，使用英语作为备用
                if (!translationData[currentLang]) {
                    console.log('找不到当前语言的翻译数据:', currentLang, '使用英语作为备用');

                    // 如果是阿拉伯语，添加阿拉伯语翻译数据
                    if (currentLang === 'ar') {
                        // 添加阿拉伯语翻译数据
                        if (!translationData['ar']) {
                            translationData['ar'] = {
                                'panama_hats_collection': 'مجموعة قبعات بنما',
                                'our_collection_title': 'مجموعتنا - قبعات بنما',
                                'filter': 'تصفية',
                                'sort': 'ترتيب',
                                'load_more': 'تحميل المزيد',
                                'add_to_cart': 'أضف إلى السلة',
                                'your_cart': 'سلة التسوق الخاصة بك',
                                'cart_empty': 'سلة التسوق الخاصة بك فارغة',
                                'subtotal': 'المجموع الفرعي',
                                'shipping': 'الشحن',
                                'free': 'مجاني',
                                'total': 'المجموع',
                                'proceed_to_checkout': 'المتابعة إلى الدفع',
                                'language': 'اللغة',
                                'footer_copyright_text': '2023 مجموعة قبعات بنما. جميع الحقوق محفوظة.',

                                // 产品标题
                                'product_1_title': 'قبعة بنما كلاسيكية',
                                'product_2_title': 'قبعة بنما فاخرة',
                                'product_3_title': 'قبعة بنما تقليدية',
                                'product_4_title': 'قبعة بنما مخصصة',
                                'product_5_title': 'قبعة بنما خفيفة قابلة للطي',
                                'product_6_title': 'قبعة بنما أنيقة',
                                'product_7_title': 'قبعة بنما يومية',
                                'product_8_title': 'قبعة بنما إكوادورية تقليدية',
                                'product_9_title': 'قبعة بنما استوائية',
                                'product_10_title': 'قبعة بنما مريحة',
                                'product_11_title': 'قبعة بنما فاخرة مميزة',
                                'product_12_title': 'قبعة بنما عصرية',

                                // 产品描述
                                'product_1_desc': 'خفيفة الوزن ومريحة للأيام المشمسة.',
                                'product_2_desc': 'تصميم أنيق للمناسبات الرسمية.',
                                'product_3_desc': 'حماية قصوى من الشمس مع الأناقة.',
                                'product_4_desc': 'قبعة متعددة الاستخدامات لأي مناسبة.',
                                'product_5_desc': 'تصميم بحري كلاسيكي مع راحة عصرية.',
                                'product_6_desc': 'أفضل قبعة بنما منسوجة يدويًا متاحة.',
                                'product_7_desc': 'راحة يومية مع أسلوب خالد.',
                                'product_8_desc': 'تصميم أصلي من الإكوادور.',
                                'product_9_desc': 'تصميم أنيق للمغامرات الاستوائية.',
                                'product_10_desc': 'راحة عادية للارتداء اليومي.',
                                'product_11_desc': 'حرفية ممتازة لعشاق الفخامة.',
                                'product_12_desc': 'أسلوب جريء للمستكشفين العصريين.',

                                // 标签
                                'tag_1_1': 'مواد مختارة',
                                'tag_1_2': 'صناعة يدوية',
                                'tag_1_3': 'تصميم كلاسيكي',
                                'tag_2_1': 'خفيفة وتنفس',
                                'tag_2_2': 'تخصيص عالي',
                                'tag_2_3': 'تصميم أنيق',
                                'tag_3_1': 'حماية من الشمس',
                                'tag_3_2': 'مناسبة مريحة',
                                'tag_3_3': 'متعددة الاستخدامات',
                                'tag_4_1': 'تصميم متعدد الوظائف',
                                'tag_4_2': 'حرفية عالية',
                                'tag_4_3': 'تفاصيل راقية',
                                'tag_5_1': 'قابلة للطي ومحمولة',
                                'tag_5_2': 'خفيفة ومريحة',
                                'tag_5_3': 'أسلوب بحري كلاسيكي',
                                'tag_6_1': 'مواد فاخرة',
                                'tag_6_2': 'تجربة فاخرة',
                                'tag_6_3': 'أسلوب حضري',
                                'tag_7_1': 'استخدام يومي',
                                'tag_7_2': 'متينة ومريحة',
                                'tag_7_3': 'تصميم كلاسيكي',
                                'tag_8_1': 'حرفية تقليدية',
                                'tag_8_2': 'مواد مختارة',
                                'tag_8_3': 'إنتاج أصلي',
                                'tag_9_1': 'أسلوب استوائي',
                                'tag_9_2': 'تصميم عصري',
                                'tag_9_3': 'قماش فاخر',
                                'tag_10_1': 'أسلوب عادي',
                                'tag_10_2': 'تجربة مريحة',
                                'tag_10_3': 'تصميم بسيط',
                                'tag_11_1': 'تخصيص فاخر',
                                'tag_11_2': 'حرفية متميزة',
                                'tag_11_3': 'تجربة فاخرة',
                                'tag_12_1': 'تصميم مبتكر',
                                'tag_12_2': 'أسلوب عصري',
                                'tag_12_3': 'روح المغامرة',

                                // 折扣标签
                                'discount_badge': '-20%',
                                'discount_badge_3': '-15%',
                                'discount_badge_6': '-25%',
                                'discount_badge_8': '-30%',
                                'discount_badge_9': '-10%',
                                'discount_badge_11': '-20%'
                            };
                            console.log('已添加阿拉伯语翻译数据');

                            // 直接应用阿拉伯语翻译
                            document.querySelectorAll('[data-i18n]').forEach(element => {
                                const key = element.getAttribute('data-i18n');
                                if (translationData['ar'][key]) {
                                    element.textContent = translationData['ar'][key];
                                    console.log('直接应用阿拉伯语翻译:', key, '->', translationData['ar'][key]);
                                }
                            });
                        }
                    }

                    // 如果是法语，添加法语翻译数据
                    if (currentLang === 'fr') {
                        // 添加法语翻译数据
                        if (!translationData['fr']) {
                            translationData['fr'] = {
                                'panama_hats_collection': 'Collection de Chapeaux Panama',
                                'our_collection_title': 'Notre Collection - Chapeaux Panama',
                                'filter': 'Filtrer',
                                'sort': 'Trier',
                                'load_more': 'Charger Plus',
                                'add_to_cart': 'Ajouter au Panier',
                                'your_cart': 'Votre Panier',
                                'cart_empty': 'Votre panier est vide',
                                'subtotal': 'Sous-total',
                                'shipping': 'Livraison',
                                'free': 'Gratuit',
                                'total': 'Total',
                                'proceed_to_checkout': 'Procéder au Paiement',
                                'language': 'Langue',
                                'footer_copyright_text': '2023 Collection de Chapeaux Panama. Tous droits réservés.',

                                // 产品标题
                                'product_1_title': 'Chapeau Panama Classique',
                                'product_2_title': 'Chapeau Panama de Luxe',
                                'product_3_title': 'Chapeau Panama Traditionnel',
                                'product_4_title': 'Chapeau Panama Personnalisé',
                                'product_5_title': 'Chapeau Panama Pliable Léger',
                                'product_6_title': 'Chapeau Panama Élégant',
                                'product_7_title': 'Chapeau Panama Quotidien',
                                'product_8_title': 'Chapeau Panama Équatorien Traditionnel',
                                'product_9_title': 'Chapeau Panama Tropical',
                                'product_10_title': 'Chapeau Panama Confortable',
                                'product_11_title': 'Chapeau Panama Premium',
                                'product_12_title': 'Chapeau Panama Moderne',

                                // 产品描述
                                'product_1_desc': 'Léger et respirant pour les journées ensoleillées.',
                                'product_2_desc': 'Style sophistiqué pour les occasions formelles.',
                                'product_3_desc': 'Protection solaire maximale avec style.',
                                'product_4_desc': 'Chapeau polyvalent pour toute occasion.',
                                'product_5_desc': 'Style nautique classique avec confort moderne.',
                                'product_6_desc': 'Le meilleur chapeau Panama tissé à la main disponible.',
                                'product_7_desc': 'Confort quotidien avec un style intemporel.',
                                'product_8_desc': 'Design authentique d\'Équateur.',
                                'product_9_desc': 'Design élégant pour les aventures tropicales.',
                                'product_10_desc': 'Confort décontracté pour un usage quotidien.',
                                'product_11_desc': 'Artisanat premium pour les amateurs de luxe.',
                                'product_12_desc': 'Style audacieux pour les explorateurs modernes.',

                                // 标签
                                'tag_1_1': 'Matériaux sélectionnés',
                                'tag_1_2': 'Fait à la main',
                                'tag_1_3': 'Design classique',
                                'tag_2_1': 'Léger et respirant',
                                'tag_2_2': 'Personnalisation haut de gamme',
                                'tag_2_3': 'Design élégant',
                                'tag_3_1': 'Protection solaire',
                                'tag_3_2': 'Ajustement confortable',
                                'tag_3_3': 'Polyvalent',
                                'tag_4_1': 'Design multifonctionnel',
                                'tag_4_2': 'Artisanat de qualité',
                                'tag_4_3': 'Détails raffinés',
                                'tag_5_1': 'Pliable et portable',
                                'tag_5_2': 'Léger et confortable',
                                'tag_5_3': 'Style marin classique',
                                'tag_6_1': 'Matériaux premium',
                                'tag_6_2': 'Expérience luxueuse',
                                'tag_6_3': 'Style urbain',
                                'tag_7_1': 'Usage quotidien',
                                'tag_7_2': 'Durable et confortable',
                                'tag_7_3': 'Design classique',
                                'tag_8_1': 'Artisanat traditionnel',
                                'tag_8_2': 'Matériaux sélectionnés',
                                'tag_8_3': 'Production d\'origine',
                                'tag_9_1': 'Style tropical',
                                'tag_9_2': 'Design à la mode',
                                'tag_9_3': 'Tissu premium',
                                'tag_10_1': 'Style décontracté',
                                'tag_10_2': 'Expérience confortable',
                                'tag_10_3': 'Design minimaliste',
                                'tag_11_1': 'Personnalisation premium',
                                'tag_11_2': 'Artisanat exceptionnel',
                                'tag_11_3': 'Expérience luxueuse',
                                'tag_12_1': 'Design innovant',
                                'tag_12_2': 'Style moderne',
                                'tag_12_3': 'Esprit d\'aventure',

                                // 折扣标签
                                'discount_badge': '-20%',
                                'discount_badge_3': '-15%',
                                'discount_badge_6': '-25%',
                                'discount_badge_8': '-30%',
                                'discount_badge_9': '-10%',
                                'discount_badge_11': '-20%'
                            };
                            console.log('已添加法语翻译数据');

                            // 直接应用法语翻译
                            document.querySelectorAll('[data-i18n]').forEach(element => {
                                const key = element.getAttribute('data-i18n');
                                if (translationData['fr'][key]) {
                                    element.textContent = translationData['fr'][key];
                                    console.log('直接应用法语翻译:', key, '->', translationData['fr'][key]);
                                }
                            });
                        }
                    }
                    // 如果是俄语，我们需要添加俄语翻译数据
                    if (currentLang === 'ru') {
                        // 添加俄语翻译数据
                        if (!translationData['ru']) {
                            translationData['ru'] = {
                                'panama_hats_collection': 'Коллекция панам',
                                'our_collection_title': 'Наша коллекция - Панамы',
                                'filter': 'Фильтр',
                                'sort': 'Сортировать',
                                'load_more': 'Загрузить ещё',
                                'add_to_cart': 'Добавить в корзину',
                                'your_cart': 'Ваша корзина',
                                'cart_empty': 'Ваша корзина пуста',
                                'subtotal': 'Промежуточный итог',
                                'shipping': 'Доставка',
                                'free': 'Бесплатно',
                                'total': 'Итого',
                                'proceed_to_checkout': 'Перейти к оформлению',
                                'language': 'Язык',
                                'footer_copyright_text': '2023 Коллекция панам. Все права защищены.',

                                // 产品标题
                                'product_1_title': 'Классическая Панама',
                                'product_2_title': 'Люксовая Панама',
                                'product_3_title': 'Традиционная Панама',
                                'product_4_title': 'Индивидуальная Панама',
                                'product_5_title': 'Лёгкая Складная Панама',
                                'product_6_title': 'Элегантная Панама',
                                'product_7_title': 'Повседневная Панама',
                                'product_8_title': 'Традиционная Эквадорская Панама',
                                'product_9_title': 'Тропическая Панама',
                                'product_10_title': 'Комфортная Панама',
                                'product_11_title': 'Премиальная Панама',
                                'product_12_title': 'Современная Панама',

                                // 产品描述
                                'product_1_desc': 'Лёгкая и дышащая для солнечных дней.',
                                'product_2_desc': 'Изысканный стиль для официальных мероприятий.',
                                'product_3_desc': 'Максимальная защита от солнца со стилем.',
                                'product_4_desc': 'Универсальная шляпа для любого случая.',
                                'product_5_desc': 'Классический морской стиль с современным комфортом.',
                                'product_6_desc': 'Лучшая доступная панама ручного плетения.',
                                'product_7_desc': 'Повседневный комфорт с вневременным стилем.',
                                'product_8_desc': 'Аутентичный дизайн из Эквадора.',
                                'product_9_desc': 'Элегантный дизайн для тропических приключений.',
                                'product_10_desc': 'Повседневный комфорт для ежедневного ношения.',
                                'product_11_desc': 'Премиальное мастерство для ценителей роскоши.',
                                'product_12_desc': 'Смелый стиль для современных исследователей.',

                                // 标签
                                'tag_1_1': 'Отборные материалы',
                                'tag_1_2': 'Ручная работа',
                                'tag_1_3': 'Классический дизайн',
                                'tag_2_1': 'Лёгкая и дышащая',
                                'tag_2_2': 'Премиальная отделка',
                                'tag_2_3': 'Элегантный дизайн',
                                'tag_3_1': 'Защита от солнца',
                                'tag_3_2': 'Комфортная посадка',
                                'tag_3_3': 'Универсальность',
                                'tag_4_1': 'Многофункциональный дизайн',
                                'tag_4_2': 'Высокое качество',
                                'tag_4_3': 'Изысканные детали',
                                'tag_5_1': 'Складная и портативная',
                                'tag_5_2': 'Лёгкая и комфортная',
                                'tag_5_3': 'Классический морской стиль',
                                'tag_6_1': 'Премиальные материалы',
                                'tag_6_2': 'Роскошный опыт',
                                'tag_6_3': 'Городской стиль',
                                'tag_7_1': 'Повседневное использование',
                                'tag_7_2': 'Прочная и комфортная',
                                'tag_7_3': 'Классический дизайн',
                                'tag_8_1': 'Традиционное мастерство',
                                'tag_8_2': 'Отборные материалы',
                                'tag_8_3': 'Оригинальное производство',
                                'tag_9_1': 'Тропический стиль',
                                'tag_9_2': 'Модный дизайн',
                                'tag_9_3': 'Премиальная ткань',
                                'tag_10_1': 'Повседневный стиль',
                                'tag_10_2': 'Комфортный опыт',
                                'tag_10_3': 'Минималистичный дизайн',
                                'tag_11_1': 'Премиальная персонализация',
                                'tag_11_2': 'Исключительное мастерство',
                                'tag_11_3': 'Роскошный опыт',
                                'tag_12_1': 'Инновационный дизайн',
                                'tag_12_2': 'Современный стиль',
                                'tag_12_3': 'Дух приключений',

                                // 折扣标签
                                'discount_badge': '-20%',
                                'discount_badge_3': '-15%',
                                'discount_badge_6': '-25%',
                                'discount_badge_8': '-30%',
                                'discount_badge_9': '-10%',
                                'discount_badge_11': '-20%'
                            };
                            console.log('已添加俄语翻译数据');

                            // 直接应用俄语翻译
                            document.querySelectorAll('[data-i18n]').forEach(element => {
                                const key = element.getAttribute('data-i18n');
                                if (translationData['ru'][key]) {
                                    element.textContent = translationData['ru'][key];
                                    console.log('直接应用俄语翻译:', key, '->', translationData['ru'][key]);
                                }
                            });
                        }
                    }
                    // 如果是日语，确保所有标签都有翻译
                    if (currentLang === 'ja') {
                        // 确保日语翻译数据完整
                        if (translationData['ja']) {
                            // 补充可能缺失的标签翻译
                            const jaTagsToAdd = {
                                'panama_hats_collection': 'パナマハットコレクション',
                                'our_collection_title': '私たちのコレクション - パナマハット',
                                'filter': 'フィルター',
                                'sort': '並べ替え',
                                'load_more': 'もっと読み込む',
                                'add_to_cart': 'カートに追加',
                                'your_cart': 'あなたのカート',
                                'cart_empty': 'カートは空です',
                                'subtotal': '小計',
                                'shipping': '配送',
                                'free': '無料',
                                'total': '合計',
                                'proceed_to_checkout': 'チェックアウトに進む',
                                'language': '言語',
                                'tag_1_1': '厳選素材',
                                'tag_1_2': 'ハンドメイド',
                                'tag_1_3': 'クラシックスタイル',
                                'tag_2_1': '軽量で通気性',
                                'tag_2_2': '高級カスタム',
                                'tag_2_3': 'エレガントデザイン',
                                'tag_3_1': '日焼け防止',
                                'tag_3_2': '快適フィット',
                                'tag_3_3': '多用途',
                                'tag_4_1': '多機能デザイン',
                                'tag_4_2': '高級工芸',
                                'tag_4_3': '洗練された細部',
                                'tag_5_1': '折りたたみ可能',
                                'tag_5_2': '軽量で快適',
                                'tag_5_3': 'クラシックマリンスタイル',
                                'tag_6_1': 'プレミアム素材',
                                'tag_6_2': '贅沢な体験',
                                'tag_6_3': 'アーバンスタイル',
                                'tag_7_1': '日常使い',
                                'tag_7_2': '耐久性と快適さ',
                                'tag_7_3': 'クラシックデザイン',
                                'tag_8_1': '伝統的な職人技',
                                'tag_8_2': '厳選された素材',
                                'tag_8_3': '原産地製造',
                                'tag_9_1': 'トロピカルスタイル',
                                'tag_9_2': 'ファッショナブルなデザイン',
                                'tag_9_3': 'プレミアム生地',
                                'tag_10_1': 'カジュアルスタイル',
                                'tag_10_2': '快適な体験',
                                'tag_10_3': 'ミニマルデザイン',
                                'tag_11_1': 'プレミアムカスタマイズ',
                                'tag_11_2': '卓越した職人技',
                                'tag_11_3': '贅沢な体験',
                                'tag_12_1': '革新的なデザイン',
                                'tag_12_2': 'モダンスタイル',
                                'tag_12_3': '冒険精神',
                                'discount_badge': '-20%',
                                'discount_badge_3': '-15%',
                                'discount_badge_6': '-25%',
                                'discount_badge_8': '-30%',
                                'discount_badge_9': '-10%',
                                'discount_badge_11': '-20%'
                            };

                            // 将缺失的翻译添加到日语翻译数据中
                            for (const key in jaTagsToAdd) {
                                if (!translationData['ja'][key]) {
                                    translationData['ja'][key] = jaTagsToAdd[key];
                                    console.log('添加日语翻译:', key, '->', jaTagsToAdd[key]);
                                }
                            }
                        }
                    }

                    // 如果仍然找不到翻译数据，使用英语
                    if (!translationData[currentLang]) {
                        return;
                    }
                }

                // 应用所有tag_开头的标签翻译
                document.querySelectorAll('[data-i18n^="tag_"]').forEach(element => {
                    const key = element.getAttribute('data-i18n');
                    if (translationData[currentLang][key]) {
                        element.textContent = translationData[currentLang][key];
                        console.log('应用标签翻译:', key, '->', translationData[currentLang][key]);
                    } else if (translationData['en'][key]) {
                        // 如果当前语言没有该翻译，使用英语
                        element.textContent = translationData['en'][key];
                        console.log('使用英语翻译:', key, '->', translationData['en'][key]);
                    } else {
                        console.warn('找不到标签翻译:', key);
                    }
                });

                // 应用所有discount_badge_开头的标签翻译
                document.querySelectorAll('[data-i18n^="discount_badge"]').forEach(element => {
                    const key = element.getAttribute('data-i18n');
                    if (translationData[currentLang][key]) {
                        element.textContent = translationData[currentLang][key];
                        console.log('应用折扣标签翻译:', key, '->', translationData[currentLang][key]);
                    } else if (translationData['en'][key]) {
                        // 如果当前语言没有该翻译，使用英语
                        element.textContent = translationData['en'][key];
                        console.log('使用英语翻译:', key, '->', translationData['en'][key]);
                    } else {
                        console.warn('找不到折扣标签翻译:', key);
                    }
                });

                // 应用所有产品标题和描述的翻译
                document.querySelectorAll('[data-i18n^="product_"]').forEach(element => {
                    const key = element.getAttribute('data-i18n');
                    if (translationData[currentLang][key]) {
                        element.textContent = translationData[currentLang][key];
                        console.log('应用产品翻译:', key, '->', translationData[currentLang][key]);
                    } else if (translationData['en'][key]) {
                        // 如果当前语言没有该翻译，使用英语
                        element.textContent = translationData['en'][key];
                        console.log('使用英语翻译:', key, '->', translationData['en'][key]);
                    } else {
                        console.warn('找不到产品翻译:', key);
                    }
                });

                // 应用所有其他元素的翻译
                document.querySelectorAll('[data-i18n]:not([data-i18n^="tag_"]):not([data-i18n^="discount_badge"]):not([data-i18n^="product_"])').forEach(element => {
                    const key = element.getAttribute('data-i18n');
                    if (translationData[currentLang][key]) {
                        element.textContent = translationData[currentLang][key];
                        console.log('应用其他元素翻译:', key, '->', translationData[currentLang][key]);
                    } else if (translationData['en'][key]) {
                        // 如果当前语言没有该翻译，使用英语
                        element.textContent = translationData['en'][key];
                        console.log('使用英语翻译:', key, '->', translationData['en'][key]);
                    } else {
                        console.warn('找不到其他元素翻译:', key);
                    }
                });
            }

            // 综合应用翻译的函数
            function applyAllTranslations() {
                console.log('综合应用所有翻译');
                const savedLanguage = localStorage.getItem('selectedLanguage') || 'zh';

                // 1. 首先尝试直接应用翻译（不依赖i18next）
                if (typeof window.applyTranslations === 'function') {
                    console.log('使用直接翻译方法');
                    window.applyTranslations();
                }

                // 2. 应用标签和其他元素翻译
                applyTagTranslations();

                // 3. 然后尝试使用i18next
                if (typeof i18next !== 'undefined' && i18next.isInitialized) {
                    console.log('i18next已初始化，直接更新内容');

                    // 切换语言
                    i18next.changeLanguage(savedLanguage).then(() => {
                        // 更新所有带有data-i18n属性的元素
                        document.querySelectorAll('[data-i18n]').forEach(element => {
                            const key = element.getAttribute('data-i18n');
                            if (i18next.exists(key)) {
                                element.textContent = i18next.t(key);
                                console.log('i18next翻译元素:', key, '->', i18next.t(key));
                            }
                        });

                        // 强制更新所有"Add to Cart"按钮
                        document.querySelectorAll('.add-to-cart span').forEach(span => {
                            if (i18next.exists('add_to_cart')) {
                                span.textContent = i18next.t('add_to_cart');
                                console.log('i18next强制更新购物车按钮文本:', i18next.t('add_to_cart'));
                            }
                        });

                        console.log('i18next语言已切换为:', savedLanguage);
                    });
                } else if (window.i18nHelper && typeof window.i18nHelper.manualInit === 'function') {
                    console.log('使用i18nHelper.manualInit手动初始化');
                    window.i18nHelper.manualInit(savedLanguage);
                }
            }

            // 应用所有翻译
            applyAllTranslations();

            // 语言选择菜单已移除，但保留多语言支持功能
            // 从URL参数中获取语言设置
            const urlParams = new URLSearchParams(window.location.search);
            const langParam = urlParams.get('lang');

            if (langParam) {
                // 如果URL中有语言参数，使用该参数
                console.log('从URL参数获取语言设置:', langParam);

                // 保存语言选择到localStorage
                localStorage.setItem('selectedLanguage', langParam);

                // 设置HTML语言属性
                document.documentElement.setAttribute('lang', langParam);

                // 设置RTL支持
                if (langParam === 'ar') {
                    document.documentElement.dir = 'rtl';
                    document.body.classList.add('rtl');
                } else {
                    document.documentElement.dir = 'ltr';
                    document.body.classList.remove('rtl');
                }

                // 立即应用所有翻译
                applyAllTranslations();

                // 触发自定义事件，通知语言已更改
                const event = new CustomEvent('languageChanged', { detail: { language: langParam } });
                document.dispatchEvent(event);
            }

            // 延迟执行一次额外的翻译应用，确保所有元素都已加载
            setTimeout(function() {
                console.log('延迟执行额外的翻译应用');
                applyAllTranslations();

                // 再次检查i18next是否已初始化
                if (typeof i18next === 'undefined' || !i18next.isInitialized) {
                    console.log('i18next未初始化，等待初始化');

                    // 等待i18next初始化
                    const checkI18next = setInterval(function() {
                        if (typeof i18next !== 'undefined' && i18next.isInitialized) {
                            clearInterval(checkI18next);
                            console.log('检测到i18next已初始化，更新内容');
                            applyAllTranslations();
                        }
                    }, 100);

                    // 2秒后如果仍未初始化，则清除定时器
                    setTimeout(function() {
                        clearInterval(checkI18next);
                        console.log('i18next初始化检查超时，使用直接翻译');
                        applyTagTranslations();
                    }, 2000);
                }
            }, 500);

            // 最后的保障措施：无论如何，2秒后再次尝试直接应用翻译
            setTimeout(function() {
                console.log('最终保障：再次尝试直接应用翻译');
                applyAllTranslations();
            }, 2000);

            // 加载购物车数据
            loadCart();

            // 检测并设置所有产品图片
            if (typeof window.initializeProductImages === 'function') {
                window.initializeProductImages();
            } else {
                console.error('图片检测功能未加载');
            }

            // 重置加载状态
            if (typeof window.resetLoadMoreState === 'function') {
                window.resetLoadMoreState('assets/img/cap/panama/list1');
            }

            // 初始化简单产品网格
            if (typeof window.initializeSimpleGrid === 'function') {
                console.log('初始化简单产品网格');
                window.initializeSimpleGrid('assets/img/cap/panama/list1');
            } else {
                console.error('简单网格初始化功能未加载');
            }
        });

        // 页面卸载时清理懒加载监听器
        window.addEventListener('beforeunload', function() {
            if (lazyLoadCleanup && typeof lazyLoadCleanup === 'function') {
                lazyLoadCleanup();
                console.log('页面卸载，懒加载监听器已清理');
            }
        });
    </script>
</body>
</html>